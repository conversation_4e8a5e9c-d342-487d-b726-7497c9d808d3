import { ChangeDetectorRef, Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { RequestService } from '../../../services/request.service';
import { ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-request-overview',
  templateUrl: './request-overview.component.html',
  styleUrls: ['./request-overview.component.scss'],
})
export class RequestOverviewComponent implements OnInit, OnDestroy {
  request: any = null;
  requestId: string | null = null;
  private routeSub: Subscription | null = null;
  private requestSub: Subscription | null = null;

  // Modal properties
  modalType: 'image' | 'video' = 'image';
  modalContent: string = '';
  modalTitle: string = '';

  constructor(
    protected cd: ChangeDetectorRef,
    protected requestService: RequestService,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
     if (this.route.parent) {
      this.routeSub = this.route.parent.paramMap.subscribe((params) => {
        this.requestId = params.get('id') || this.requestService.getRequestId();
        console.log('RequestOverviewComponent - Request ID:', this.requestId);

        if (this.requestId) {
           this.requestSub = this.requestService.getRequest().subscribe((request) => {
            this.request = request;
            console.log('RequestOverviewComponent - Request Data from Service:', this.request);
            this.cd.detectChanges();

             if (!this.request) {
              this.fetchRequest();
            }
          });
        } else {
          console.error('RequestOverviewComponent - No request ID found');
          Swal.fire('Invalid request ID.', '', 'error');
        }
      });
    } else {
      // Handle case where parent route is not available
      this.routeSub = null;
      this.requestId = this.requestService.getRequestId();
      console.error('RequestOverviewComponent - Parent route not found, fallback requestId:', this.requestId);
      if (this.requestId) {
        this.requestSub = this.requestService.getRequest().subscribe((request) => {
          this.request = request;
          console.log('RequestOverviewComponent - Request Data from Service:', this.request);
          this.cd.detectChanges();
          if (!this.request) {
            this.fetchRequest();
          }
        });
      } else {
        console.error('RequestOverviewComponent - No request ID available');
        Swal.fire('Invalid request ID.', '', 'error');
      }
    }
  }

  ngOnDestroy() {
    if (this.routeSub) {
      this.routeSub.unsubscribe();
    }
    if (this.requestSub) {
      this.requestSub.unsubscribe();
    }
  }

  fetchRequest() {
    if (this.requestId) {
      this.requestService.getRequestById(this.requestId).subscribe({
        next: (response: any) => {
          this.request = response.data;
          this.requestService.setRequest(this.request);
          console.log('RequestOverviewComponent - Fetched Request Data:', this.request);
          this.cd.detectChanges();
        },
        error: (error: any) => {
          console.error('RequestOverviewComponent - Error fetching request:', error);
          this.cd.detectChanges();
          Swal.fire('Failed to load data. Please try again later.', '', 'error');
        },
      });
    }
  }

  // Modal methods
  openImageModal(imageUrl: string, title: string) {
    this.modalType = 'image';
    this.modalContent = imageUrl;
    this.modalTitle = title;
  }

  openVideoModal(videoUrl: string, title: string = 'Video') {
    this.modalType = 'video';
    this.modalContent = videoUrl;
    this.modalTitle = title;
  }
}
