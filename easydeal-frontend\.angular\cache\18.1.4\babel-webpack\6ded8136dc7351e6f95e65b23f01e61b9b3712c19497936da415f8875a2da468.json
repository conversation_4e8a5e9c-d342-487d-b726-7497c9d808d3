{"ast": null, "code": "import Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/request.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"../../../broker/shared/broker-title/broker-title.component\";\nconst _c0 = () => [\"history\"];\nconst _c1 = a0 => ({\n  requestUserId: a0\n});\nfunction RenderRequestComponent_div_24_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"a\", 32);\n    i0.ɵɵlistener(\"click\", function RenderRequestComponent_div_24_div_1_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.updateRequestStatus(ctx_r1.request.id, ctx_r1.userId, \"in_processing\"));\n    });\n    i0.ɵɵelement(2, \"i\", 33);\n    i0.ɵɵtext(3, \" Start Processing \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction RenderRequestComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, RenderRequestComponent_div_24_div_1_Template, 4, 0, \"div\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.request == null ? null : ctx_r1.request.status) == \"new\");\n  }\n}\nfunction RenderRequestComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"a\", 34);\n    i0.ɵɵlistener(\"click\", function RenderRequestComponent_div_25_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.archiveRequest(ctx_r1.request.id, ctx_r1.brokerId));\n    });\n    i0.ɵɵelement(2, \"i\", 35);\n    i0.ɵɵtext(3, \" Archive \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction RenderRequestComponent_li_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 28)(1, \"a\", 36);\n    i0.ɵɵtext(2, \" Units Recommendation \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class RenderRequestComponent {\n  cd;\n  requestService;\n  route;\n  router;\n  userId;\n  brokerId;\n  request = null;\n  requestId = null;\n  canReply = true;\n  routeSub = null;\n  user;\n  constructor(cd, requestService, route, router) {\n    this.cd = cd;\n    this.requestService = requestService;\n    this.route = route;\n    this.router = router;\n  }\n  ngOnInit() {\n    const userJson = localStorage.getItem('currentUser');\n    this.user = userJson ? JSON.parse(userJson) : null;\n    this.userId = this.user?.id;\n    this.brokerId = this.user?.brokerId;\n    this.routeSub = this.route.paramMap.subscribe(params => {\n      this.requestId = params.get('id');\n      console.log('RenderRequestComponent - Request ID:', this.requestId);\n      if (this.requestId) {\n        this.requestService.setRequestId(this.requestId);\n        this.requestService.clearRequest(); // Clear previous request data\n        this.request = null; // Reset local request\n        this.cd.markForCheck();\n        this.getRequest();\n      } else {\n        console.error('RenderRequestComponent - No request ID found in route');\n        Swal.fire('Invalid request ID.', '', 'error');\n      }\n    });\n  }\n  ngOnDestroy() {\n    if (this.routeSub) {\n      this.routeSub.unsubscribe();\n    }\n  }\n  getRequest() {\n    if (this.requestId) {\n      this.requestService.getRequestById(this.requestId).subscribe({\n        next: response => {\n          this.request = response.data;\n          console.log('RenderRequestComponent - Request Data:', this.request);\n          console.log('RenderRequestComponent - Request canReply:', this.canReply);\n          this.requestService.setRequest(this.request);\n          this.checkReplyAvailability();\n          this.cd.markForCheck();\n        },\n        error: error => {\n          console.error('RenderRequestComponent - Error fetching request:', error);\n          this.cd.markForCheck();\n          Swal.fire('Failed to load data. Please try again later.', '', 'error');\n        }\n      });\n    }\n  }\n  checkReplyAvailability() {\n    this.canReply = this.request?.user?.id !== this.userId;\n    console.log('RenderRequestComponent - canReply:', this.canReply);\n    this.cd.markForCheck();\n    return this.canReply;\n  }\n  updateRequestStatus(requestId, userId, status) {\n    const payload = {\n      userId,\n      status\n    };\n    this.requestService.updateRequestStatus(requestId, payload).subscribe({\n      next: response => {\n        console.log('Status updated successfully:', response);\n        this.request.status = status;\n        this.cd.markForCheck();\n        Swal.fire('Request status updated successfully!', '', 'success');\n      },\n      error: error => {\n        console.error('Error updating status:', error);\n      }\n    });\n  }\n  archiveRequest(requestId, brokerId) {\n    this.requestService.archiveRequest(requestId, brokerId).subscribe({\n      next: response => {\n        console.log('Request archived successfully:', response);\n        this.cd.markForCheck();\n        Swal.fire({\n          title: 'Request archived successfully!',\n          icon: 'success',\n          confirmButtonText: 'OK'\n        }).then(result => {\n          if (result.isConfirmed) {\n            this.router.navigate(['/requests/received']);\n          }\n        });\n      },\n      error: error => {\n        console.error('Error archiving request:', error);\n        Swal.fire('Failed to archive request', '', 'error');\n      }\n    });\n  }\n  static ɵfac = function RenderRequestComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || RenderRequestComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.RequestService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: RenderRequestComponent,\n    selectors: [[\"app-render-request\"]],\n    decls: 47,\n    vars: 14,\n    consts: [[1, \"mb-5\", \"mt-0\"], [1, \"card\", \"mb-5\", \"mb-xl-10\"], [1, \"card-body\", \"pt-5\", \"pb-0\"], [1, \"d-flex\", \"flex-wrap\", \"flex-sm-nowrap\", \"mb-3\"], [1, \"flex-grow-1\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-start\", \"flex-wrap\", \"mb-2\"], [1, \"d-flex\", \"flex-column\", \"my-4\"], [1, \"d-flex\", \"align-items-center\", \"mb-2\"], [1, \"text-gray-800\", \"fs-2\", \"fw-bolder\", \"me-1\"], [1, \"d-flex\", \"flex-wrap\", \"fw-bold\", \"fs-5\", \"mb-4\", \"pe-2\"], [1, \"d-flex\", \"align-items-center\", \"text-gray-600\", \"me-5\", \"mb-2\"], [1, \"fa-solid\", \"fa-user\", \"me-1\", \"text-mid-blue\"], [1, \"fa-solid\", \"fa-calendar\", \"me-1\", \"text-mid-blue\"], [1, \"fa-solid\", \"fa-phone\", \"me-1\", \"text-mid-blue\"], [1, \"d-flex\", \"my-4\"], [1, \"badge\", \"badge-light-dark-blue\", \"px-3\", \"py-3\", \"me-3\", \"fw-bold\", \"fs-6\"], [4, \"ngIf\"], [1, \"d-flex\", \"flex-wrap\", \"flex-stack\"], [1, \"d-flex\", \"flex-column\", \"flex-grow-1\", \"pe-8\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"border\", \"border-gray-300\", \"border-dashed\", \"rounded\", \"min-w-125px\", \"py-3\", \"px-4\", \"me-6\", \"mb-3\"], [1, \"d-flex\", \"align-items-center\"], [1, \"fs-2\", \"fw-bolder\"], [1, \"fa-solid\", \"fa-reply\", \"me-1\", \"text-mid-blue\"], [1, \"fw-bold\", \"fs-6\", \"text-gray-500\"], [1, \"d-flex\", \"overflow-auto\", \"mb-5\"], [1, \"nav\", \"nav-stretch\", \"nav-line-tabs\", \"nav-line-tabs-2x\", \"border-transparent\", \"fs-5\", \"fw-bolder\", \"flex-nowrap\"], [\"class\", \"nav-item\", 4, \"ngIf\"], [1, \"nav-item\"], [\"routerLinkActive\", \"active\", 1, \"nav-link\", \"me-6\", \"btn\", \"btn-active-dark-blue\", \"btn-light-primary\", 3, \"routerLink\", \"queryParams\"], [\"routerLink\", \"replies\", \"routerLinkActive\", \"active\", 1, \"nav-link\", \"me-6\", \"btn\", \"btn-active-dark-blue\", \"btn-light-primary\"], [1, \"card-body\", \"pt-3\", \"pb-0\", \"px-0\"], [1, \"btn\", \"btn-sm\", \"btn-mid-blue\", \"me-3\", \"cursor-pointer\", \"fw-bold\", \"fs-6\", 3, \"click\"], [1, \"fa-solid\", \"fa-play\"], [1, \"btn\", \"btn-sm\", \"btn-danger\", \"me-3\", \"cursor-pointer\", \"fw-bold\", \"fs-6\", 3, \"click\"], [1, \"fa-regular\", \"fa-eye-slash\"], [\"routerLink\", \"units-recommendation\", \"routerLinkActive\", \"active\", 1, \"nav-link\", \"me-6\", \"btn\", \"btn-active-dark-blue\", \"btn-light-primary\"]],\n    template: function RenderRequestComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵelement(1, \"app-broker-title\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(2, \"div\", 1)(3, \"div\", 2)(4, \"div\", 3)(5, \"div\", 4)(6, \"div\", 5)(7, \"div\", 6)(8, \"div\", 7)(9, \"span\", 8);\n        i0.ɵɵtext(10);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"div\", 9)(12, \"span\", 10);\n        i0.ɵɵelement(13, \"i\", 11);\n        i0.ɵɵtext(14);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(15, \"span\", 10);\n        i0.ɵɵelement(16, \"i\", 12);\n        i0.ɵɵtext(17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(18, \"span\", 10);\n        i0.ɵɵelement(19, \"i\", 13);\n        i0.ɵɵtext(20);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(21, \"div\", 14)(22, \"span\", 15);\n        i0.ɵɵtext(23);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(24, RenderRequestComponent_div_24_Template, 2, 1, \"div\", 16)(25, RenderRequestComponent_div_25_Template, 4, 0, \"div\", 16);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(26, \"div\", 17)(27, \"div\", 18)(28, \"div\", 19)(29, \"div\", 20)(30, \"div\", 21)(31, \"div\", 22);\n        i0.ɵɵelement(32, \"i\", 23);\n        i0.ɵɵtext(33);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(34, \"div\", 24);\n        i0.ɵɵtext(35, \"Replies\");\n        i0.ɵɵelementEnd()()()()()()();\n        i0.ɵɵelementStart(36, \"div\", 25)(37, \"ul\", 26);\n        i0.ɵɵtemplate(38, RenderRequestComponent_li_38_Template, 3, 0, \"li\", 27);\n        i0.ɵɵelementStart(39, \"li\", 28)(40, \"a\", 29);\n        i0.ɵɵtext(41, \" History \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(42, \"li\", 28)(43, \"a\", 30);\n        i0.ɵɵtext(44, \" Replies \");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(45, \"div\", 31);\n        i0.ɵɵelement(46, \"router-outlet\");\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(10);\n        i0.ɵɵtextInterpolate((ctx.request == null ? null : ctx.request.title) || \"Loading...\");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", (ctx.request == null ? null : ctx.request.user == null ? null : ctx.request.user.name) || \"N/A\", \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", (ctx.request == null ? null : ctx.request.createdAt) || \"N/A\", \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", (ctx.request == null ? null : ctx.request.user == null ? null : ctx.request.user.phone) || \"N/A\", \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", (ctx.request == null ? null : ctx.request.status) || \"N/A\", \" \");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", (ctx.user == null ? null : ctx.user.role) == \"broker\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", (ctx.user == null ? null : ctx.user.role) == \"broker\");\n        i0.ɵɵadvance(8);\n        i0.ɵɵtextInterpolate1(\" \", (ctx.request == null ? null : ctx.request.numberOfReplies) || 0, \" \");\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ctx.canReply);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(11, _c0))(\"queryParams\", i0.ɵɵpureFunction1(12, _c1, ctx.request == null ? null : ctx.request.user == null ? null : ctx.request.user.id));\n      }\n    },\n    dependencies: [i3.NgIf, i2.RouterOutlet, i2.RouterLink, i2.RouterLinkActive, i4.BrokerTitleComponent],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵlistener", "RenderRequestComponent_div_24_div_1_Template_a_click_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "updateRequestStatus", "request", "id", "userId", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "RenderRequestComponent_div_24_div_1_Template", "ɵɵadvance", "ɵɵproperty", "status", "RenderRequestComponent_div_25_Template_a_click_1_listener", "_r3", "archiveRequest", "brokerId", "RenderRequestComponent", "cd", "requestService", "route", "router", "requestId", "canReply", "routeSub", "user", "constructor", "ngOnInit", "userJson", "localStorage", "getItem", "JSON", "parse", "paramMap", "subscribe", "params", "get", "console", "log", "setRequestId", "clearRequest", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getRequest", "error", "fire", "ngOnDestroy", "unsubscribe", "getRequestById", "next", "response", "data", "setRequest", "checkReplyAvailability", "payload", "title", "icon", "confirmButtonText", "then", "result", "isConfirmed", "navigate", "ɵɵdirectiveInject", "ChangeDetectorRef", "i1", "RequestService", "i2", "ActivatedRoute", "Router", "selectors", "decls", "vars", "consts", "template", "RenderRequestComponent_Template", "rf", "ctx", "RenderRequestComponent_div_24_Template", "RenderRequestComponent_div_25_Template", "RenderRequestComponent_li_38_Template", "ɵɵtextInterpolate", "ɵɵtextInterpolate1", "name", "createdAt", "phone", "role", "numberOfReplies", "ɵɵpureFunction0", "_c0", "ɵɵpureFunction1", "_c1"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\requests\\components\\render-request\\render-request.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\requests\\components\\render-request\\render-request.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, OnInit, OnDestroy } from '@angular/core';\r\nimport { RequestService } from '../../services/request.service';\r\nimport { ActivatedRoute, ParamMap, Router } from '@angular/router';\r\nimport { Subscription } from 'rxjs';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'app-render-request',\r\n  templateUrl: './render-request.component.html',\r\n  styleUrls: ['./render-request.component.scss'],\r\n})\r\nexport class RenderRequestComponent implements OnInit, OnDestroy {\r\n\r\n  userId :number;\r\n  brokerId :number ;\r\n  request: any = null;\r\n  requestId: string | null = null;\r\n  canReply: boolean = true;\r\n  private routeSub: Subscription | null = null;\r\n  user: any;\r\n\r\n\r\n  constructor(\r\n    protected cd: ChangeDetectorRef,\r\n    protected requestService: RequestService,\r\n    private route: ActivatedRoute,\r\n    private router: Router\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    const userJson = localStorage.getItem('currentUser');\r\n    this.user = userJson ? JSON.parse(userJson) : null;\r\n    this.userId = this.user?.id;\r\n    this.brokerId = this.user?.brokerId;\r\n    this.routeSub = this.route.paramMap.subscribe((params: ParamMap) => {\r\n      this.requestId = params.get('id');\r\n      console.log('RenderRequestComponent - Request ID:', this.requestId);\r\n      if (this.requestId) {\r\n        this.requestService.setRequestId(this.requestId);\r\n        this.requestService.clearRequest(); // Clear previous request data\r\n        this.request = null; // Reset local request\r\n        this.cd.markForCheck();\r\n        this.getRequest();\r\n      } else {\r\n        console.error('RenderRequestComponent - No request ID found in route');\r\n        Swal.fire('Invalid request ID.', '', 'error');\r\n      }\r\n    });\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    if (this.routeSub) {\r\n      this.routeSub.unsubscribe();\r\n    }\r\n  }\r\n\r\n  getRequest() {\r\n    if (this.requestId) {\r\n      this.requestService.getRequestById(this.requestId).subscribe({\r\n        next: (response: any) => {\r\n          this.request = response.data;\r\n          console.log('RenderRequestComponent - Request Data:', this.request);\r\n          console.log('RenderRequestComponent - Request canReply:', this.canReply);\r\n          this.requestService.setRequest(this.request);\r\n          this.checkReplyAvailability();\r\n          this.cd.markForCheck();\r\n        },\r\n        error: (error: any) => {\r\n          console.error('RenderRequestComponent - Error fetching request:', error);\r\n          this.cd.markForCheck();\r\n          Swal.fire('Failed to load data. Please try again later.', '', 'error');\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  checkReplyAvailability(): boolean {\r\n    this.canReply = this.request?.user?.id !== this.userId;\r\n    console.log('RenderRequestComponent - canReply:', this.canReply);\r\n    this.cd.markForCheck();\r\n    return this.canReply;\r\n  }\r\n\r\n  updateRequestStatus(requestId: number, userId: number, status: string) {\r\n    const payload = {\r\n      userId,\r\n      status,\r\n    };\r\n\r\n    this.requestService.updateRequestStatus(requestId, payload).subscribe({\r\n      next: (response) => {\r\n        console.log('Status updated successfully:', response);\r\n        this.request.status = status;\r\n        this.cd.markForCheck();\r\n        Swal.fire('Request status updated successfully!', '', 'success');\r\n      },\r\n      error: (error) => {\r\n        console.error('Error updating status:', error);\r\n      },\r\n    });\r\n  }\r\n\r\n  archiveRequest(requestId: number, brokerId: number) {\r\n    this.requestService.archiveRequest(requestId, brokerId).subscribe({\r\n      next: (response) => {\r\n        console.log('Request archived successfully:', response);\r\n        this.cd.markForCheck();\r\n\r\n        Swal.fire({\r\n          title: 'Request archived successfully!',\r\n          icon: 'success',\r\n          confirmButtonText: 'OK'\r\n        }).then((result) => {\r\n          if (result.isConfirmed) {\r\n            this.router.navigate(['/requests/received']);\r\n          }\r\n        });\r\n      },\r\n      error: (error) => {\r\n        console.error('Error archiving request:', error);\r\n        Swal.fire('Failed to archive request', '', 'error');\r\n      },\r\n    });\r\n  }\r\n}\r\n", "<div class=\"mb-5 mt-0\">\r\n  <app-broker-title></app-broker-title>\r\n</div>\r\n\r\n<div class=\"card mb-5 mb-xl-10\">\r\n  <div class=\"card-body pt-5 pb-0\">\r\n    <div class=\"d-flex flex-wrap flex-sm-nowrap mb-3\">\r\n      <div class=\"flex-grow-1\">\r\n        <div class=\"d-flex justify-content-between align-items-start flex-wrap mb-2\">\r\n          <div class=\"d-flex flex-column my-4\">\r\n            <div class=\"d-flex align-items-center mb-2\">\r\n              <span class=\"text-gray-800 fs-2 fw-bolder me-1\">{{ request?.title || 'Loading...' }}</span>\r\n            </div>\r\n\r\n            <div class=\"d-flex flex-wrap fw-bold fs-5 mb-4 pe-2\">\r\n              <span class=\"d-flex align-items-center text-gray-600 me-5 mb-2\">\r\n                <i class=\"fa-solid fa-user me-1 text-mid-blue\"></i>\r\n                {{ request?.user?.name || 'N/A' }}\r\n              </span>\r\n              <span class=\"d-flex align-items-center text-gray-600 me-5 mb-2\">\r\n                <i class=\"fa-solid fa-calendar me-1 text-mid-blue\"></i>\r\n                {{ request?.createdAt || 'N/A' }}\r\n              </span>\r\n              <span class=\"d-flex align-items-center text-gray-600 me-5 mb-2\">\r\n                <i class=\"fa-solid fa-phone me-1 text-mid-blue\"></i>\r\n                {{ request?.user?.phone || 'N/A' }}\r\n              </span>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"d-flex my-4\">\r\n            <span class=\"badge badge-light-dark-blue px-3 py-3 me-3 fw-bold fs-6\">\r\n              {{ request?.status || 'N/A' }}\r\n            </span>\r\n            <!--broker actions-->\r\n            <div *ngIf=\"user?.role == 'broker'\">\r\n              <div *ngIf=\"request?.status == 'new'\">\r\n                <a class=\"btn btn-sm btn-mid-blue me-3 cursor-pointer fw-bold fs-6\"\r\n                  (click)=\"updateRequestStatus(request.id, userId, 'in_processing')\">\r\n                  <i class=\"fa-solid fa-play\"></i>\r\n                  Start Processing\r\n                </a>\r\n              </div>\r\n            </div>\r\n            <div *ngIf=\"user?.role == 'broker'\">\r\n              <a class=\"btn btn-sm btn-danger me-3 cursor-pointer fw-bold fs-6\"\r\n                (click)=\"archiveRequest(request.id, brokerId)\">\r\n                <i class=\"fa-regular fa-eye-slash\"></i>\r\n                Archive\r\n              </a>\r\n            </div>\r\n            <!--client actions-->\r\n            <!-- <div *ngIf=\"user?.role == 'client'\">\r\n              <div *ngIf=\"request?.status != 'finished'\">\r\n                <a class=\"btn btn-sm btn-mid-blue me-3 cursor-pointer fw-bold fs-6\" (click)=\"updateRequestStatus(request.id, userId, 'finished')\">\r\n                  <i class=\"fa-solid fa-close\"></i>\r\n                  Finish Request\r\n                </a>\r\n              </div>\r\n            </div> -->\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"d-flex flex-wrap flex-stack\">\r\n          <div class=\"d-flex flex-column flex-grow-1 pe-8\">\r\n            <div class=\"d-flex flex-wrap\">\r\n              <div class=\"border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 me-6 mb-3\">\r\n                <div class=\"d-flex align-items-center\">\r\n                  <div class=\"fs-2 fw-bolder\">\r\n                    <i class=\"fa-solid fa-reply me-1 text-mid-blue\"></i>\r\n                    {{ request?.numberOfReplies || 0 }}\r\n                  </div>\r\n                </div>\r\n                <div class=\"fw-bold fs-6 text-gray-500\">Replies</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"d-flex overflow-auto mb-5\">\r\n      <ul class=\"nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-5 fw-bolder flex-nowrap\">\r\n        <!-- <li class=\"nav-item\">\r\n          <a\r\n            class=\"nav-link me-6 btn btn-active-dark-blue btn-light-primary\"\r\n            routerLink=\"overview\"\r\n            routerLinkActive=\"active\"\r\n          >\r\n            Overview\r\n          </a>\r\n        </li> -->\r\n        <li class=\"nav-item\" *ngIf=\"canReply\">\r\n          <a class=\"nav-link me-6 btn btn-active-dark-blue btn-light-primary\" routerLink=\"units-recommendation\"\r\n            routerLinkActive=\"active\">\r\n            Units Recommendation\r\n          </a>\r\n        </li>\r\n        <li class=\"nav-item\">\r\n          <a class=\"nav-link me-6 btn btn-active-dark-blue btn-light-primary\" [routerLink]=\"['history']\"\r\n            [queryParams]=\"{ requestUserId: request?.user?.id }\" routerLinkActive=\"active\">\r\n            History\r\n          </a>\r\n        </li>\r\n        <li class=\"nav-item\">\r\n          <a class=\"nav-link me-6 btn btn-active-dark-blue btn-light-primary\" routerLink=\"replies\"\r\n            routerLinkActive=\"active\">\r\n            Replies\r\n          </a>\r\n        </li>\r\n      </ul>\r\n    </div>\r\n\r\n    <div class=\"card-body pt-3 pb-0 px-0\">\r\n      <router-outlet></router-outlet>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAIA,OAAOA,IAAI,MAAM,aAAa;;;;;;;;;;;;;ICiCdC,EADF,CAAAC,cAAA,UAAsC,YAEiC;IAAnED,EAAA,CAAAE,UAAA,mBAAAC,gEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,mBAAA,CAAAH,MAAA,CAAAI,OAAA,CAAAC,EAAA,EAAAL,MAAA,CAAAM,MAAA,EAAwC,eAAe,CAAC;IAAA,EAAC;IAClEZ,EAAA,CAAAa,SAAA,YAAgC;IAChCb,EAAA,CAAAc,MAAA,yBACF;IACFd,EADE,CAAAe,YAAA,EAAI,EACA;;;;;IAPRf,EAAA,CAAAC,cAAA,UAAoC;IAClCD,EAAA,CAAAgB,UAAA,IAAAC,4CAAA,kBAAsC;IAOxCjB,EAAA,CAAAe,YAAA,EAAM;;;;IAPEf,EAAA,CAAAkB,SAAA,EAA8B;IAA9BlB,EAAA,CAAAmB,UAAA,UAAAb,MAAA,CAAAI,OAAA,kBAAAJ,MAAA,CAAAI,OAAA,CAAAU,MAAA,WAA8B;;;;;;IASpCpB,EADF,CAAAC,cAAA,UAAoC,YAEe;IAA/CD,EAAA,CAAAE,UAAA,mBAAAmB,0DAAA;MAAArB,EAAA,CAAAI,aAAA,CAAAkB,GAAA;MAAA,MAAAhB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiB,cAAA,CAAAjB,MAAA,CAAAI,OAAA,CAAAC,EAAA,EAAAL,MAAA,CAAAkB,QAAA,CAAoC;IAAA,EAAC;IAC9CxB,EAAA,CAAAa,SAAA,YAAuC;IACvCb,EAAA,CAAAc,MAAA,gBACF;IACFd,EADE,CAAAe,YAAA,EAAI,EACA;;;;;IA2CRf,EADF,CAAAC,cAAA,aAAsC,YAER;IAC1BD,EAAA,CAAAc,MAAA,6BACF;IACFd,EADE,CAAAe,YAAA,EAAI,EACD;;;ADtFb,OAAM,MAAOU,sBAAsB;EAYrBC,EAAA;EACAC,cAAA;EACFC,KAAA;EACAC,MAAA;EAbVjB,MAAM;EACNY,QAAQ;EACRd,OAAO,GAAQ,IAAI;EACnBoB,SAAS,GAAkB,IAAI;EAC/BC,QAAQ,GAAY,IAAI;EAChBC,QAAQ,GAAwB,IAAI;EAC5CC,IAAI;EAGJC,YACYR,EAAqB,EACrBC,cAA8B,EAChCC,KAAqB,EACrBC,MAAc;IAHZ,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,cAAc,GAAdA,cAAc;IAChB,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHM,QAAQA,CAAA;IACN,MAAMC,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACpD,IAAI,CAACL,IAAI,GAAGG,QAAQ,GAAGG,IAAI,CAACC,KAAK,CAACJ,QAAQ,CAAC,GAAG,IAAI;IAClD,IAAI,CAACxB,MAAM,GAAG,IAAI,CAACqB,IAAI,EAAEtB,EAAE;IAC3B,IAAI,CAACa,QAAQ,GAAG,IAAI,CAACS,IAAI,EAAET,QAAQ;IACnC,IAAI,CAACQ,QAAQ,GAAG,IAAI,CAACJ,KAAK,CAACa,QAAQ,CAACC,SAAS,CAAEC,MAAgB,IAAI;MACjE,IAAI,CAACb,SAAS,GAAGa,MAAM,CAACC,GAAG,CAAC,IAAI,CAAC;MACjCC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAAChB,SAAS,CAAC;MACnE,IAAI,IAAI,CAACA,SAAS,EAAE;QAClB,IAAI,CAACH,cAAc,CAACoB,YAAY,CAAC,IAAI,CAACjB,SAAS,CAAC;QAChD,IAAI,CAACH,cAAc,CAACqB,YAAY,EAAE,CAAC,CAAC;QACpC,IAAI,CAACtC,OAAO,GAAG,IAAI,CAAC,CAAC;QACrB,IAAI,CAACgB,EAAE,CAACuB,YAAY,EAAE;QACtB,IAAI,CAACC,UAAU,EAAE;MACnB,CAAC,MAAM;QACLL,OAAO,CAACM,KAAK,CAAC,uDAAuD,CAAC;QACtEpD,IAAI,CAACqD,IAAI,CAAC,qBAAqB,EAAE,EAAE,EAAE,OAAO,CAAC;MAC/C;IACF,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACrB,QAAQ,EAAE;MACjB,IAAI,CAACA,QAAQ,CAACsB,WAAW,EAAE;IAC7B;EACF;EAEAJ,UAAUA,CAAA;IACR,IAAI,IAAI,CAACpB,SAAS,EAAE;MAClB,IAAI,CAACH,cAAc,CAAC4B,cAAc,CAAC,IAAI,CAACzB,SAAS,CAAC,CAACY,SAAS,CAAC;QAC3Dc,IAAI,EAAGC,QAAa,IAAI;UACtB,IAAI,CAAC/C,OAAO,GAAG+C,QAAQ,CAACC,IAAI;UAC5Bb,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE,IAAI,CAACpC,OAAO,CAAC;UACnEmC,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE,IAAI,CAACf,QAAQ,CAAC;UACxE,IAAI,CAACJ,cAAc,CAACgC,UAAU,CAAC,IAAI,CAACjD,OAAO,CAAC;UAC5C,IAAI,CAACkD,sBAAsB,EAAE;UAC7B,IAAI,CAAClC,EAAE,CAACuB,YAAY,EAAE;QACxB,CAAC;QACDE,KAAK,EAAGA,KAAU,IAAI;UACpBN,OAAO,CAACM,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;UACxE,IAAI,CAACzB,EAAE,CAACuB,YAAY,EAAE;UACtBlD,IAAI,CAACqD,IAAI,CAAC,8CAA8C,EAAE,EAAE,EAAE,OAAO,CAAC;QACxE;OACD,CAAC;IACJ;EACF;EAEAQ,sBAAsBA,CAAA;IACpB,IAAI,CAAC7B,QAAQ,GAAG,IAAI,CAACrB,OAAO,EAAEuB,IAAI,EAAEtB,EAAE,KAAK,IAAI,CAACC,MAAM;IACtDiC,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE,IAAI,CAACf,QAAQ,CAAC;IAChE,IAAI,CAACL,EAAE,CAACuB,YAAY,EAAE;IACtB,OAAO,IAAI,CAAClB,QAAQ;EACtB;EAEAtB,mBAAmBA,CAACqB,SAAiB,EAAElB,MAAc,EAAEQ,MAAc;IACnE,MAAMyC,OAAO,GAAG;MACdjD,MAAM;MACNQ;KACD;IAED,IAAI,CAACO,cAAc,CAAClB,mBAAmB,CAACqB,SAAS,EAAE+B,OAAO,CAAC,CAACnB,SAAS,CAAC;MACpEc,IAAI,EAAGC,QAAQ,IAAI;QACjBZ,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEW,QAAQ,CAAC;QACrD,IAAI,CAAC/C,OAAO,CAACU,MAAM,GAAGA,MAAM;QAC5B,IAAI,CAACM,EAAE,CAACuB,YAAY,EAAE;QACtBlD,IAAI,CAACqD,IAAI,CAAC,sCAAsC,EAAE,EAAE,EAAE,SAAS,CAAC;MAClE,CAAC;MACDD,KAAK,EAAGA,KAAK,IAAI;QACfN,OAAO,CAACM,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAChD;KACD,CAAC;EACJ;EAEA5B,cAAcA,CAACO,SAAiB,EAAEN,QAAgB;IAChD,IAAI,CAACG,cAAc,CAACJ,cAAc,CAACO,SAAS,EAAEN,QAAQ,CAAC,CAACkB,SAAS,CAAC;MAChEc,IAAI,EAAGC,QAAQ,IAAI;QACjBZ,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEW,QAAQ,CAAC;QACvD,IAAI,CAAC/B,EAAE,CAACuB,YAAY,EAAE;QAEtBlD,IAAI,CAACqD,IAAI,CAAC;UACRU,KAAK,EAAE,gCAAgC;UACvCC,IAAI,EAAE,SAAS;UACfC,iBAAiB,EAAE;SACpB,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAI;UACjB,IAAIA,MAAM,CAACC,WAAW,EAAE;YACtB,IAAI,CAACtC,MAAM,CAACuC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;UAC9C;QACF,CAAC,CAAC;MACJ,CAAC;MACDjB,KAAK,EAAGA,KAAK,IAAI;QACfN,OAAO,CAACM,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChDpD,IAAI,CAACqD,IAAI,CAAC,2BAA2B,EAAE,EAAE,EAAE,OAAO,CAAC;MACrD;KACD,CAAC;EACJ;;qCAhHW3B,sBAAsB,EAAAzB,EAAA,CAAAqE,iBAAA,CAAArE,EAAA,CAAAsE,iBAAA,GAAAtE,EAAA,CAAAqE,iBAAA,CAAAE,EAAA,CAAAC,cAAA,GAAAxE,EAAA,CAAAqE,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAA1E,EAAA,CAAAqE,iBAAA,CAAAI,EAAA,CAAAE,MAAA;EAAA;;UAAtBlD,sBAAsB;IAAAmD,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCXnClF,EAAA,CAAAC,cAAA,aAAuB;QACrBD,EAAA,CAAAa,SAAA,uBAAqC;QACvCb,EAAA,CAAAe,YAAA,EAAM;QASQf,EAPd,CAAAC,cAAA,aAAgC,aACG,aACmB,aACvB,aACsD,aACtC,aACS,cACM;QAAAD,EAAA,CAAAc,MAAA,IAAoC;QACtFd,EADsF,CAAAe,YAAA,EAAO,EACvF;QAGJf,EADF,CAAAC,cAAA,cAAqD,gBACa;QAC9DD,EAAA,CAAAa,SAAA,aAAmD;QACnDb,EAAA,CAAAc,MAAA,IACF;QAAAd,EAAA,CAAAe,YAAA,EAAO;QACPf,EAAA,CAAAC,cAAA,gBAAgE;QAC9DD,EAAA,CAAAa,SAAA,aAAuD;QACvDb,EAAA,CAAAc,MAAA,IACF;QAAAd,EAAA,CAAAe,YAAA,EAAO;QACPf,EAAA,CAAAC,cAAA,gBAAgE;QAC9DD,EAAA,CAAAa,SAAA,aAAoD;QACpDb,EAAA,CAAAc,MAAA,IACF;QAEJd,EAFI,CAAAe,YAAA,EAAO,EACH,EACF;QAGJf,EADF,CAAAC,cAAA,eAAyB,gBAC+C;QACpED,EAAA,CAAAc,MAAA,IACF;QAAAd,EAAA,CAAAe,YAAA,EAAO;QAWPf,EATA,CAAAgB,UAAA,KAAAoE,sCAAA,kBAAoC,KAAAC,sCAAA,kBASA;QAiBxCrF,EADE,CAAAe,YAAA,EAAM,EACF;QAOIf,EALV,CAAAC,cAAA,eAAyC,eACU,eACjB,eAC8D,eACjD,eACT;QAC1BD,EAAA,CAAAa,SAAA,aAAoD;QACpDb,EAAA,CAAAc,MAAA,IACF;QACFd,EADE,CAAAe,YAAA,EAAM,EACF;QACNf,EAAA,CAAAC,cAAA,eAAwC;QAAAD,EAAA,CAAAc,MAAA,eAAO;QAM3Dd,EAN2D,CAAAe,YAAA,EAAM,EACjD,EACF,EACF,EACF,EACF,EACF;QAGJf,EADF,CAAAC,cAAA,eAAuC,cACoE;QAUvGD,EAAA,CAAAgB,UAAA,KAAAsE,qCAAA,iBAAsC;QAOpCtF,EADF,CAAAC,cAAA,cAAqB,aAE8D;QAC/ED,EAAA,CAAAc,MAAA,iBACF;QACFd,EADE,CAAAe,YAAA,EAAI,EACD;QAEHf,EADF,CAAAC,cAAA,cAAqB,aAES;QAC1BD,EAAA,CAAAc,MAAA,iBACF;QAGNd,EAHM,CAAAe,YAAA,EAAI,EACD,EACF,EACD;QAENf,EAAA,CAAAC,cAAA,eAAsC;QACpCD,EAAA,CAAAa,SAAA,qBAA+B;QAGrCb,EAFI,CAAAe,YAAA,EAAM,EACF,EACF;;;QA1GwDf,EAAA,CAAAkB,SAAA,IAAoC;QAApClB,EAAA,CAAAuF,iBAAA,EAAAJ,GAAA,CAAAzE,OAAA,kBAAAyE,GAAA,CAAAzE,OAAA,CAAAoD,KAAA,kBAAoC;QAMlF9D,EAAA,CAAAkB,SAAA,GACF;QADElB,EAAA,CAAAwF,kBAAA,OAAAL,GAAA,CAAAzE,OAAA,kBAAAyE,GAAA,CAAAzE,OAAA,CAAAuB,IAAA,kBAAAkD,GAAA,CAAAzE,OAAA,CAAAuB,IAAA,CAAAwD,IAAA,gBACF;QAGEzF,EAAA,CAAAkB,SAAA,GACF;QADElB,EAAA,CAAAwF,kBAAA,OAAAL,GAAA,CAAAzE,OAAA,kBAAAyE,GAAA,CAAAzE,OAAA,CAAAgF,SAAA,gBACF;QAGE1F,EAAA,CAAAkB,SAAA,GACF;QADElB,EAAA,CAAAwF,kBAAA,OAAAL,GAAA,CAAAzE,OAAA,kBAAAyE,GAAA,CAAAzE,OAAA,CAAAuB,IAAA,kBAAAkD,GAAA,CAAAzE,OAAA,CAAAuB,IAAA,CAAA0D,KAAA,gBACF;QAMA3F,EAAA,CAAAkB,SAAA,GACF;QADElB,EAAA,CAAAwF,kBAAA,OAAAL,GAAA,CAAAzE,OAAA,kBAAAyE,GAAA,CAAAzE,OAAA,CAAAU,MAAA,gBACF;QAEMpB,EAAA,CAAAkB,SAAA,EAA4B;QAA5BlB,EAAA,CAAAmB,UAAA,UAAAgE,GAAA,CAAAlD,IAAA,kBAAAkD,GAAA,CAAAlD,IAAA,CAAA2D,IAAA,cAA4B;QAS5B5F,EAAA,CAAAkB,SAAA,EAA4B;QAA5BlB,EAAA,CAAAmB,UAAA,UAAAgE,GAAA,CAAAlD,IAAA,kBAAAkD,GAAA,CAAAlD,IAAA,CAAA2D,IAAA,cAA4B;QA0B1B5F,EAAA,CAAAkB,SAAA,GACF;QADElB,EAAA,CAAAwF,kBAAA,OAAAL,GAAA,CAAAzE,OAAA,kBAAAyE,GAAA,CAAAzE,OAAA,CAAAmF,eAAA,YACF;QAqBY7F,EAAA,CAAAkB,SAAA,GAAc;QAAdlB,EAAA,CAAAmB,UAAA,SAAAgE,GAAA,CAAApD,QAAA,CAAc;QAOkC/B,EAAA,CAAAkB,SAAA,GAA0B;QAC5FlB,EADkE,CAAAmB,UAAA,eAAAnB,EAAA,CAAA8F,eAAA,KAAAC,GAAA,EAA0B,gBAAA/F,EAAA,CAAAgG,eAAA,KAAAC,GAAA,EAAAd,GAAA,CAAAzE,OAAA,kBAAAyE,GAAA,CAAAzE,OAAA,CAAAuB,IAAA,kBAAAkD,GAAA,CAAAzE,OAAA,CAAAuB,IAAA,CAAAtB,EAAA,EACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}