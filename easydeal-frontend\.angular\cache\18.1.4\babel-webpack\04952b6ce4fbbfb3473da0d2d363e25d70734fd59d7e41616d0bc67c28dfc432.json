{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/profile.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nconst _c0 = [\"profileForm\"];\nfunction ProfileDetailsComponent_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Full name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileDetailsComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtemplate(1, ProfileDetailsComponent_div_15_div_1_Template, 2, 0, \"div\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const fullName_r2 = i0.ɵɵreference(14);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", fullName_r2.errors == null ? null : fullName_r2.errors[\"required\"]);\n  }\n}\nfunction ProfileDetailsComponent_div_23_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Phone number is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileDetailsComponent_div_23_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Phone number must be a valid Egyptian number. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileDetailsComponent_div_23_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Phone number must be 11 digits. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileDetailsComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtemplate(1, ProfileDetailsComponent_div_23_div_1_Template, 2, 0, \"div\", 22)(2, ProfileDetailsComponent_div_23_div_2_Template, 2, 0, \"div\", 22)(3, ProfileDetailsComponent_div_23_div_3_Template, 2, 0, \"div\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const phoneNumber_r3 = i0.ɵɵreference(22);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", phoneNumber_r3.errors == null ? null : phoneNumber_r3.errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", phoneNumber_r3.errors == null ? null : phoneNumber_r3.errors[\"pattern\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (phoneNumber_r3.errors == null ? null : phoneNumber_r3.errors[\"minlength\"]) || (phoneNumber_r3.errors == null ? null : phoneNumber_r3.errors[\"maxlength\"]));\n  }\n}\nfunction ProfileDetailsComponent_span_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 23);\n  }\n}\nexport class ProfileDetailsComponent {\n  profileService;\n  user = {};\n  isLoading = false;\n  saveChanges = new EventEmitter();\n  loadingError = new EventEmitter();\n  profileForm;\n  formData = {};\n  localLoading = false;\n  constructor(profileService) {\n    this.profileService = profileService;\n  }\n  ngOnChanges(changes) {\n    if (changes['user'] && this.user) {\n      this.formData = {\n        ...this.user\n      };\n    }\n  }\n  onSubmit() {\n    this.localLoading = true;\n    const userData = {\n      fullName: this.profileForm.value.fullName,\n      phone: this.user.phone,\n      email: this.user.email\n    };\n    this.profileService.updateProfile(this.user.id, userData).subscribe({\n      next: response => {\n        this.localLoading = false;\n        console.log(response.data);\n        localStorage.setItem('currentUser', JSON.stringify(response.data));\n        Swal.fire('Success', 'Profile updated successfullyqqqqqqqqq', 'success');\n      },\n      error: error => {\n        this.localLoading = false;\n        console.error('Error updating profile:', error);\n        Swal.fire('Error', 'Failed to update profile', 'error');\n      }\n    });\n  }\n  showLoadingForOneSecond() {\n    this.localLoading = true;\n    setTimeout(() => {\n      this.localLoading = false;\n    }, 1000);\n  }\n  static ɵfac = function ProfileDetailsComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ProfileDetailsComponent)(i0.ɵɵdirectiveInject(i1.ProfileService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProfileDetailsComponent,\n    selectors: [[\"app-profile-details\"]],\n    viewQuery: function ProfileDetailsComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.profileForm = _t.first);\n      }\n    },\n    inputs: {\n      user: \"user\",\n      isLoading: \"isLoading\"\n    },\n    outputs: {\n      saveChanges: \"saveChanges\",\n      loadingError: \"loadingError\"\n    },\n    standalone: true,\n    features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n    decls: 28,\n    vars: 9,\n    consts: [[\"profileForm\", \"ngForm\"], [\"fullName\", \"ngModel\"], [\"phoneNumber\", \"ngModel\"], [1, \"card\", \"mb-5\", \"mb-xl-10\"], [\"role\", \"button\", \"data-bs-toggle\", \"collapse\", \"data-bs-target\", \"#kt_account_profile_details\", \"aria-expanded\", \"true\", \"aria-controls\", \"kt_account_profile_details\", 1, \"card-header\", \"border-0\", \"bg-light-dark-blue\"], [1, \"card-title\", \"m-0\"], [1, \"fw-bolder\", \"m-0\"], [\"id\", \"kt_account_profile_details\", 1, \"collapse\", \"show\"], [3, \"ngSubmit\"], [1, \"card-body\", \"border-top\", \"p-9\"], [1, \"row\", \"mb-6\"], [1, \"col-lg-4\", \"col-form-label\", \"required\", \"fw-bold\", \"fs-6\"], [1, \"col-lg-8\", \"fv-row\"], [\"type\", \"text\", \"placeholder\", \"Full name\", \"name\", \"fullName\", \"required\", \"\", 1, \"form-control\", \"form-control-lg\", \"form-control-solid\", \"mb-3\", \"mb-lg-0\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"class\", \"text-danger mt-2\", 4, \"ngIf\"], [1, \"col-lg-4\", \"col-form-label\", \"fw-bold\", \"fs-6\"], [1, \"required\"], [\"type\", \"tel\", \"placeholder\", \"Phone number\", \"name\", \"phoneNumber\", \"required\", \"\", \"pattern\", \"^01[0-2,5]{1}[0-9]{8}$\", \"minlength\", \"11\", \"maxlength\", \"11\", 1, \"form-control\", \"form-control-lg\", \"form-control-solid\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [1, \"card-footer\", \"d-flex\", \"justify-content-end\", \"py-6\", \"px-9\"], [\"type\", \"submit\", 1, \"btn\", \"btn-dark-blue\", \"btn-active-light-dark-blue\", 3, \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", \"role\", \"status\", 4, \"ngIf\"], [1, \"text-danger\", \"mt-2\"], [4, \"ngIf\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"]],\n    template: function ProfileDetailsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"h3\", 6);\n        i0.ɵɵtext(4, \"Profile Details\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(5, \"div\", 7)(6, \"form\", 8, 0);\n        i0.ɵɵlistener(\"ngSubmit\", function ProfileDetailsComponent_Template_form_ngSubmit_6_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onSubmit());\n        });\n        i0.ɵɵelementStart(8, \"div\", 9)(9, \"div\", 10)(10, \"label\", 11);\n        i0.ɵɵtext(11, \"Full Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"div\", 12)(13, \"input\", 13, 1);\n        i0.ɵɵtwoWayListener(\"ngModelChange\", function ProfileDetailsComponent_Template_input_ngModelChange_13_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          i0.ɵɵtwoWayBindingSet(ctx.user.fullName, $event) || (ctx.user.fullName = $event);\n          return i0.ɵɵresetView($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(15, ProfileDetailsComponent_div_15_Template, 2, 1, \"div\", 14);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(16, \"div\", 10)(17, \"label\", 15)(18, \"span\", 16);\n        i0.ɵɵtext(19, \"Phone Number\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(20, \"div\", 12)(21, \"input\", 17, 2);\n        i0.ɵɵtwoWayListener(\"ngModelChange\", function ProfileDetailsComponent_Template_input_ngModelChange_21_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          i0.ɵɵtwoWayBindingSet(ctx.user.phone, $event) || (ctx.user.phone = $event);\n          return i0.ɵɵresetView($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(23, ProfileDetailsComponent_div_23_Template, 4, 3, \"div\", 14);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(24, \"div\", 18)(25, \"button\", 19);\n        i0.ɵɵtemplate(26, ProfileDetailsComponent_span_26_Template, 1, 0, \"span\", 20);\n        i0.ɵɵtext(27);\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        const profileForm_r4 = i0.ɵɵreference(7);\n        const fullName_r2 = i0.ɵɵreference(14);\n        const phoneNumber_r3 = i0.ɵɵreference(22);\n        i0.ɵɵadvance(13);\n        i0.ɵɵtwoWayProperty(\"ngModel\", ctx.user.fullName);\n        i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", fullName_r2.invalid && (fullName_r2.dirty || fullName_r2.touched));\n        i0.ɵɵadvance(6);\n        i0.ɵɵtwoWayProperty(\"ngModel\", ctx.user.phone);\n        i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", phoneNumber_r3.invalid && (phoneNumber_r3.dirty || phoneNumber_r3.touched));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"disabled\", ctx.localLoading || profileForm_r4.invalid);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.localLoading);\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate1(\" \", ctx.localLoading ? \"Saving...\" : \"Save Changes\", \" \");\n      }\n    },\n    dependencies: [CommonModule, i2.NgIf, FormsModule, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.RequiredValidator, i3.MinLengthValidator, i3.MaxLengthValidator, i3.PatternValidator, i3.NgModel, i3.NgForm, ReactiveFormsModule],\n    styles: [\".image-input[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: inline-block;\\n  border-radius: 0.475rem;\\n  background-repeat: no-repeat;\\n  background-size: cover;\\n}\\n.image-input[_ngcontent-%COMP%]   .image-input-wrapper[_ngcontent-%COMP%] {\\n  width: 120px;\\n  height: 120px;\\n  border-radius: 0.475rem;\\n  background-repeat: no-repeat;\\n  background-size: cover;\\n  background-position: center;\\n}\\n.image-input.image-input-empty[_ngcontent-%COMP%]   .image-input-wrapper[_ngcontent-%COMP%] {\\n  background-image: url('data:image/svg+xml,%3Csvg xmlns=\\\"http://www.w3.org/2000/svg\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\"%3E%3Cpath fill=\\\"%23cccccc\\\" d=\\\"M12 6c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2m0 10c2.7 0 5.8 1.29 6 2H6c.23-.72 3.31-2 6-2m0-12C9.79 4 8 5.79 8 8s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm0 10c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z\\\"%3E%3C/path%3E%3C/svg%3E');\\n  background-color: #f8f9fa;\\n}\\n.image-input[_ngcontent-%COMP%]   [data-kt-image-input-action][_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  position: absolute;\\n  transform: translate(-50%, -50%);\\n}\\n.image-input[_ngcontent-%COMP%]   [data-kt-image-input-action][data-kt-image-input-action=change][_ngcontent-%COMP%] {\\n  left: 100%;\\n  top: 0;\\n}\\n.image-input[_ngcontent-%COMP%]   [data-kt-image-input-action][data-kt-image-input-action=remove][_ngcontent-%COMP%] {\\n  left: 100%;\\n  top: 100%;\\n}\\n.image-input[_ngcontent-%COMP%]   [data-kt-image-input-action][_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 0 !important;\\n  height: 0 !important;\\n  overflow: hidden;\\n  opacity: 0;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "ReactiveFormsModule", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "ProfileDetailsComponent_div_15_div_1_Template", "ɵɵadvance", "ɵɵproperty", "fullName_r2", "errors", "ProfileDetailsComponent_div_23_div_1_Template", "ProfileDetailsComponent_div_23_div_2_Template", "ProfileDetailsComponent_div_23_div_3_Template", "phoneNumber_r3", "ɵɵelement", "ProfileDetailsComponent", "profileService", "user", "isLoading", "saveChanges", "loadingError", "profileForm", "formData", "localLoading", "constructor", "ngOnChanges", "changes", "onSubmit", "userData", "fullName", "value", "phone", "email", "updateProfile", "id", "subscribe", "next", "response", "console", "log", "data", "localStorage", "setItem", "JSON", "stringify", "fire", "error", "showLoadingForOneSecond", "setTimeout", "ɵɵdirectiveInject", "i1", "ProfileService", "selectors", "viewQuery", "ProfileDetailsComponent_Query", "rf", "ctx", "ɵɵlistener", "ProfileDetailsComponent_Template_form_ngSubmit_6_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView", "ɵɵtwoWayListener", "ProfileDetailsComponent_Template_input_ngModelChange_13_listener", "$event", "ɵɵtwoWayBindingSet", "ProfileDetailsComponent_div_15_Template", "ProfileDetailsComponent_Template_input_ngModelChange_21_listener", "ProfileDetailsComponent_div_23_Template", "ProfileDetailsComponent_span_26_Template", "ɵɵtwoWayProperty", "invalid", "dirty", "touched", "profileForm_r4", "ɵɵtextInterpolate1", "i2", "NgIf", "i3", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "MinLengthValidator", "MaxLengthValidator", "Pat<PERSON>Vali<PERSON><PERSON>", "NgModel", "NgForm", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\shared\\profile\\components\\profile-details\\profile-details.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\shared\\profile\\components\\profile-details\\profile-details.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  EventEmitter,\r\n  Input,\r\n  OnChanges,\r\n  Output,\r\n  SimpleChanges,\r\n  ViewChild,\r\n} from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule, ReactiveFormsModule, NgForm } from '@angular/forms';\r\nimport Swal from 'sweetalert2';\r\nimport { ProfileService } from '../../services/profile.service';\r\n\r\n@Component({\r\n  selector: 'app-profile-details',\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule, ReactiveFormsModule],\r\n  templateUrl: './profile-details.component.html',\r\n  styleUrl: './profile-details.component.scss',\r\n})\r\nexport class ProfileDetailsComponent implements OnChanges {\r\n  @Input() user: any = {};\r\n  @Input() isLoading: boolean = false;\r\n  @Output() saveChanges = new EventEmitter<any>();\r\n  @Output() loadingError = new EventEmitter<void>();\r\n  @ViewChild('profileForm') profileForm: NgForm;\r\n\r\n  formData: any = {};\r\n  localLoading: boolean = false;\r\n  constructor(private profileService: ProfileService) {}\r\n\r\n  ngOnChanges(changes: SimpleChanges): void {\r\n    if (changes['user'] && this.user) {\r\n      this.formData = { ...this.user };\r\n    }\r\n  }\r\n\r\n  onSubmit() {\r\n    this.localLoading = true;\r\n\r\n    const userData = {\r\n      fullName: this.profileForm.value.fullName,\r\n      phone: this.user.phone,\r\n      email: this.user.email,\r\n    };\r\n\r\n    this.profileService.updateProfile(this.user.id, userData).subscribe({\r\n      next: (response) => {\r\n        this.localLoading = false;\r\n        console.log(response.data);\r\n        localStorage.setItem('currentUser', JSON.stringify(response.data));\r\n        Swal.fire('Success', 'Profile updated successfullyqqqqqqqqq', 'success');\r\n      },\r\n      error: (error) => {\r\n        this.localLoading = false;\r\n        console.error('Error updating profile:', error);\r\n        Swal.fire('Error', 'Failed to update profile', 'error');\r\n      },\r\n    });\r\n  }\r\n\r\n  showLoadingForOneSecond() {\r\n    this.localLoading = true;\r\n    setTimeout(() => {\r\n      this.localLoading = false;\r\n    }, 1000);\r\n  }\r\n}\r\n", "<!-- profile details -->\r\n<div class=\"card mb-5 mb-xl-10\">\r\n  <div class=\"card-header border-0 bg-light-dark-blue\" role=\"button\" data-bs-toggle=\"collapse\"\r\n    data-bs-target=\"#kt_account_profile_details\" aria-expanded=\"true\" aria-controls=\"kt_account_profile_details\">\r\n    <div class=\"card-title m-0\">\r\n      <h3 class=\"fw-bolder m-0\">Profile Details</h3>\r\n    </div>\r\n  </div>\r\n  <div id=\"kt_account_profile_details\" class=\"collapse show\">\r\n    <form #profileForm=\"ngForm\" (ngSubmit)=\"onSubmit()\">\r\n      <div class=\"card-body border-top p-9\">\r\n        <!-- Full Name -->\r\n        <div class=\"row mb-6\">\r\n          <label class=\"col-lg-4 col-form-label required fw-bold fs-6\">Full Name</label>\r\n          <div class=\"col-lg-8 fv-row\">\r\n            <input type=\"text\" class=\"form-control form-control-lg form-control-solid mb-3 mb-lg-0\"\r\n              placeholder=\"Full name\" name=\"fullName\" [(ngModel)]=\"user.fullName\" [disabled]=\"isLoading\" required\r\n              #fullName=\"ngModel\" />\r\n            <div *ngIf=\"fullName.invalid && (fullName.dirty || fullName.touched)\" class=\"text-danger mt-2\">\r\n              <div *ngIf=\"fullName.errors?.['required']\">\r\n                Full name is required.\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Phone Number -->\r\n        <div class=\"row mb-6\">\r\n          <label class=\"col-lg-4 col-form-label fw-bold fs-6\">\r\n            <span class=\"required\">Phone Number</span>\r\n          </label>\r\n          <div class=\"col-lg-8 fv-row\">\r\n            <input type=\"tel\" class=\"form-control form-control-lg form-control-solid\" placeholder=\"Phone number\"\r\n              name=\"phoneNumber\" [(ngModel)]=\"user.phone\" [disabled]=\"isLoading\" required #phoneNumber=\"ngModel\"\r\n              pattern=\"^01[0-2,5]{1}[0-9]{8}$\" minlength=\"11\" maxlength=\"11\" />\r\n            <div *ngIf=\"\r\n                phoneNumber.invalid &&\r\n                (phoneNumber.dirty || phoneNumber.touched)\r\n              \" class=\"text-danger mt-2\">\r\n              <div *ngIf=\"phoneNumber.errors?.['required']\">\r\n                Phone number is required.\r\n              </div>\r\n              <div *ngIf=\"phoneNumber.errors?.['pattern']\">\r\n                Phone number must be a valid Egyptian number.\r\n              </div>\r\n              <div *ngIf=\"phoneNumber.errors?.['minlength'] || phoneNumber.errors?.['maxlength']\">\r\n                Phone number must be 11 digits.\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"card-footer d-flex justify-content-end py-6 px-9\">\r\n        <button type=\"submit\" class=\"btn btn-dark-blue btn-active-light-dark-blue\"\r\n          [disabled]=\"localLoading || profileForm.invalid\">\r\n          <span *ngIf=\"localLoading\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span>\r\n          {{ localLoading ? 'Saving...' : 'Save Changes' }}\r\n        </button>\r\n      </div>\r\n    </form>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAAA,SAEEA,YAAY,QAMP,eAAe;AACtB,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAgB,gBAAgB;AACzE,OAAOC,IAAI,MAAM,aAAa;;;;;;;;ICQhBC,EAAA,CAAAC,cAAA,UAA2C;IACzCD,EAAA,CAAAE,MAAA,+BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHRH,EAAA,CAAAC,cAAA,cAA+F;IAC7FD,EAAA,CAAAI,UAAA,IAAAC,6CAAA,kBAA2C;IAG7CL,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAM,SAAA,EAAmC;IAAnCN,EAAA,CAAAO,UAAA,SAAAC,WAAA,CAAAC,MAAA,kBAAAD,WAAA,CAAAC,MAAA,aAAmC;;;;;IAoBzCT,EAAA,CAAAC,cAAA,UAA8C;IAC5CD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAC,cAAA,UAA6C;IAC3CD,EAAA,CAAAE,MAAA,sDACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAC,cAAA,UAAoF;IAClFD,EAAA,CAAAE,MAAA,wCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAZRH,EAAA,CAAAC,cAAA,cAG6B;IAO3BD,EANA,CAAAI,UAAA,IAAAM,6CAAA,kBAA8C,IAAAC,6CAAA,kBAGD,IAAAC,6CAAA,kBAGuC;IAGtFZ,EAAA,CAAAG,YAAA,EAAM;;;;;IATEH,EAAA,CAAAM,SAAA,EAAsC;IAAtCN,EAAA,CAAAO,UAAA,SAAAM,cAAA,CAAAJ,MAAA,kBAAAI,cAAA,CAAAJ,MAAA,aAAsC;IAGtCT,EAAA,CAAAM,SAAA,EAAqC;IAArCN,EAAA,CAAAO,UAAA,SAAAM,cAAA,CAAAJ,MAAA,kBAAAI,cAAA,CAAAJ,MAAA,YAAqC;IAGrCT,EAAA,CAAAM,SAAA,EAA4E;IAA5EN,EAAA,CAAAO,UAAA,UAAAM,cAAA,CAAAJ,MAAA,kBAAAI,cAAA,CAAAJ,MAAA,mBAAAI,cAAA,CAAAJ,MAAA,kBAAAI,cAAA,CAAAJ,MAAA,eAA4E;;;;;IAUtFT,EAAA,CAAAc,SAAA,eAA8F;;;ADlCxG,OAAM,MAAOC,uBAAuB;EASdC,cAAA;EARXC,IAAI,GAAQ,EAAE;EACdC,SAAS,GAAY,KAAK;EACzBC,WAAW,GAAG,IAAIxB,YAAY,EAAO;EACrCyB,YAAY,GAAG,IAAIzB,YAAY,EAAQ;EACvB0B,WAAW;EAErCC,QAAQ,GAAQ,EAAE;EAClBC,YAAY,GAAY,KAAK;EAC7BC,YAAoBR,cAA8B;IAA9B,KAAAA,cAAc,GAAdA,cAAc;EAAmB;EAErDS,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAACT,IAAI,EAAE;MAChC,IAAI,CAACK,QAAQ,GAAG;QAAE,GAAG,IAAI,CAACL;MAAI,CAAE;IAClC;EACF;EAEAU,QAAQA,CAAA;IACN,IAAI,CAACJ,YAAY,GAAG,IAAI;IAExB,MAAMK,QAAQ,GAAG;MACfC,QAAQ,EAAE,IAAI,CAACR,WAAW,CAACS,KAAK,CAACD,QAAQ;MACzCE,KAAK,EAAE,IAAI,CAACd,IAAI,CAACc,KAAK;MACtBC,KAAK,EAAE,IAAI,CAACf,IAAI,CAACe;KAClB;IAED,IAAI,CAAChB,cAAc,CAACiB,aAAa,CAAC,IAAI,CAAChB,IAAI,CAACiB,EAAE,EAAEN,QAAQ,CAAC,CAACO,SAAS,CAAC;MAClEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACd,YAAY,GAAG,KAAK;QACzBe,OAAO,CAACC,GAAG,CAACF,QAAQ,CAACG,IAAI,CAAC;QAC1BC,YAAY,CAACC,OAAO,CAAC,aAAa,EAAEC,IAAI,CAACC,SAAS,CAACP,QAAQ,CAACG,IAAI,CAAC,CAAC;QAClEzC,IAAI,CAAC8C,IAAI,CAAC,SAAS,EAAE,uCAAuC,EAAE,SAAS,CAAC;MAC1E,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACvB,YAAY,GAAG,KAAK;QACzBe,OAAO,CAACQ,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C/C,IAAI,CAAC8C,IAAI,CAAC,OAAO,EAAE,0BAA0B,EAAE,OAAO,CAAC;MACzD;KACD,CAAC;EACJ;EAEAE,uBAAuBA,CAAA;IACrB,IAAI,CAACxB,YAAY,GAAG,IAAI;IACxByB,UAAU,CAAC,MAAK;MACd,IAAI,CAACzB,YAAY,GAAG,KAAK;IAC3B,CAAC,EAAE,IAAI,CAAC;EACV;;qCA9CWR,uBAAuB,EAAAf,EAAA,CAAAiD,iBAAA,CAAAC,EAAA,CAAAC,cAAA;EAAA;;UAAvBpC,uBAAuB;IAAAqC,SAAA;IAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;QChB9BvD,EAJN,CAAAC,cAAA,aAAgC,aAEiF,aACjF,YACA;QAAAD,EAAA,CAAAE,MAAA,sBAAe;QAE7CF,EAF6C,CAAAG,YAAA,EAAK,EAC1C,EACF;QAEJH,EADF,CAAAC,cAAA,aAA2D,iBACL;QAAxBD,EAAA,CAAAyD,UAAA,sBAAAC,0DAAA;UAAA1D,EAAA,CAAA2D,aAAA,CAAAC,GAAA;UAAA,OAAA5D,EAAA,CAAA6D,WAAA,CAAYL,GAAA,CAAA7B,QAAA,EAAU;QAAA,EAAC;QAI7C3B,EAHJ,CAAAC,cAAA,aAAsC,cAEd,iBACyC;QAAAD,EAAA,CAAAE,MAAA,iBAAS;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAE5EH,EADF,CAAAC,cAAA,eAA6B,oBAGH;QADkBD,EAAA,CAAA8D,gBAAA,2BAAAC,iEAAAC,MAAA;UAAAhE,EAAA,CAAA2D,aAAA,CAAAC,GAAA;UAAA5D,EAAA,CAAAiE,kBAAA,CAAAT,GAAA,CAAAvC,IAAA,CAAAY,QAAA,EAAAmC,MAAA,MAAAR,GAAA,CAAAvC,IAAA,CAAAY,QAAA,GAAAmC,MAAA;UAAA,OAAAhE,EAAA,CAAA6D,WAAA,CAAAG,MAAA;QAAA,EAA2B;QADrEhE,EAAA,CAAAG,YAAA,EAEwB;QACxBH,EAAA,CAAAI,UAAA,KAAA8D,uCAAA,kBAA+F;QAMnGlE,EADE,CAAAG,YAAA,EAAM,EACF;QAKFH,EAFJ,CAAAC,cAAA,eAAsB,iBACgC,gBAC3B;QAAAD,EAAA,CAAAE,MAAA,oBAAY;QACrCF,EADqC,CAAAG,YAAA,EAAO,EACpC;QAENH,EADF,CAAAC,cAAA,eAA6B,oBAGwC;QAD9CD,EAAA,CAAA8D,gBAAA,2BAAAK,iEAAAH,MAAA;UAAAhE,EAAA,CAAA2D,aAAA,CAAAC,GAAA;UAAA5D,EAAA,CAAAiE,kBAAA,CAAAT,GAAA,CAAAvC,IAAA,CAAAc,KAAA,EAAAiC,MAAA,MAAAR,GAAA,CAAAvC,IAAA,CAAAc,KAAA,GAAAiC,MAAA;UAAA,OAAAhE,EAAA,CAAA6D,WAAA,CAAAG,MAAA;QAAA,EAAwB;QAD7ChE,EAAA,CAAAG,YAAA,EAEmE;QACnEH,EAAA,CAAAI,UAAA,KAAAgE,uCAAA,kBAG6B;QAanCpE,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;QAEJH,EADF,CAAAC,cAAA,eAA8D,kBAET;QACjDD,EAAA,CAAAI,UAAA,KAAAiE,wCAAA,mBAAuF;QACvFrE,EAAA,CAAAE,MAAA,IACF;QAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACD,EACH,EACF;;;;;;QA7CgDH,EAAA,CAAAM,SAAA,IAA2B;QAA3BN,EAAA,CAAAsE,gBAAA,YAAAd,GAAA,CAAAvC,IAAA,CAAAY,QAAA,CAA2B;QAAC7B,EAAA,CAAAO,UAAA,aAAAiD,GAAA,CAAAtC,SAAA,CAAsB;QAEtFlB,EAAA,CAAAM,SAAA,GAA8D;QAA9DN,EAAA,CAAAO,UAAA,SAAAC,WAAA,CAAA+D,OAAA,KAAA/D,WAAA,CAAAgE,KAAA,IAAAhE,WAAA,CAAAiE,OAAA,EAA8D;QAe/CzE,EAAA,CAAAM,SAAA,GAAwB;QAAxBN,EAAA,CAAAsE,gBAAA,YAAAd,GAAA,CAAAvC,IAAA,CAAAc,KAAA,CAAwB;QAAC/B,EAAA,CAAAO,UAAA,aAAAiD,GAAA,CAAAtC,SAAA,CAAsB;QAE9DlB,EAAA,CAAAM,SAAA,GAGJ;QAHIN,EAAA,CAAAO,UAAA,SAAAM,cAAA,CAAA0D,OAAA,KAAA1D,cAAA,CAAA2D,KAAA,IAAA3D,cAAA,CAAA4D,OAAA,EAGJ;QAgBJzE,EAAA,CAAAM,SAAA,GAAgD;QAAhDN,EAAA,CAAAO,UAAA,aAAAiD,GAAA,CAAAjC,YAAA,IAAAmD,cAAA,CAAAH,OAAA,CAAgD;QACzCvE,EAAA,CAAAM,SAAA,EAAkB;QAAlBN,EAAA,CAAAO,UAAA,SAAAiD,GAAA,CAAAjC,YAAA,CAAkB;QACzBvB,EAAA,CAAAM,SAAA,EACF;QADEN,EAAA,CAAA2E,kBAAA,MAAAnB,GAAA,CAAAjC,YAAA,qCACF;;;mBDxCI3B,YAAY,EAAAgF,EAAA,CAAAC,IAAA,EAAEhF,WAAW,EAAAiF,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,oBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,oBAAA,EAAAJ,EAAA,CAAAK,iBAAA,EAAAL,EAAA,CAAAM,kBAAA,EAAAN,EAAA,CAAAO,kBAAA,EAAAP,EAAA,CAAAQ,gBAAA,EAAAR,EAAA,CAAAS,OAAA,EAAAT,EAAA,CAAAU,MAAA,EAAE1F,mBAAmB;IAAA2F,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}