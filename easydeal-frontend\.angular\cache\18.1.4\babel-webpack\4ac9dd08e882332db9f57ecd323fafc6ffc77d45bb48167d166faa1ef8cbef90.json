{"ast": null, "code": "import Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/request.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nconst _c0 = (a0, a1) => ({\n  \"badge-light-success\": a0,\n  \"badge-light-danger\": a1\n});\nfunction RequestOverviewComponent_div_25_div_8_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 9)(2, \"span\", 10);\n    i0.ɵɵtext(3, \"City:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 11);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 9)(7, \"span\", 10);\n    i0.ɵɵtext(8, \"Area:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 11);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 9)(12, \"span\", 10);\n    i0.ɵɵtext(13, \"Subarea:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 11);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const subarea_r1 = ctx.$implicit;\n    const areaItem_r2 = i0.ɵɵnextContext().$implicit;\n    const cityItem_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", cityItem_r3.city.name_en, \" | \", cityItem_r3.city.name_ar, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", areaItem_r2.area.name_en, \" | \", areaItem_r2.area.name_ar, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", subarea_r1.name_en, \" | \", subarea_r1.name_ar, \"\");\n  }\n}\nfunction RequestOverviewComponent_div_25_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, RequestOverviewComponent_div_25_div_8_div_1_div_1_Template, 16, 6, \"div\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const areaItem_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", areaItem_r2.sub_areas);\n  }\n}\nfunction RequestOverviewComponent_div_25_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, RequestOverviewComponent_div_25_div_8_div_1_Template, 2, 1, \"div\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const cityItem_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", cityItem_r3.areas);\n  }\n}\nfunction RequestOverviewComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"h6\", 3)(2, \"i\", 27);\n    i0.ɵɵelement(3, \"span\", 5)(4, \"span\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Location Details \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 28)(7, \"div\", 29);\n    i0.ɵɵtemplate(8, RequestOverviewComponent_div_25_div_8_Template, 2, 1, \"div\", 30);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.request == null ? null : ctx_r3.request.locations);\n  }\n}\nfunction RequestOverviewComponent_div_26_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"span\", 10);\n    i0.ɵɵtext(2, \"Detailed Address:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 11);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((ctx_r3.request == null ? null : ctx_r3.request.detailedAddress) || (ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.detailedAddress));\n  }\n}\nfunction RequestOverviewComponent_div_26_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"span\", 10);\n    i0.ɵɵtext(2, \"Address Link:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 33);\n    i0.ɵɵtext(4, \"View on Map\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", ctx_r3.request.attributes.addressLink, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction RequestOverviewComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"h6\", 3)(2, \"i\", 31);\n    i0.ɵɵelement(3, \"span\", 5)(4, \"span\", 6)(5, \"span\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" Address Information \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 8);\n    i0.ɵɵtemplate(8, RequestOverviewComponent_div_26_div_8_Template, 5, 1, \"div\", 32)(9, RequestOverviewComponent_div_26_div_9_Template, 5, 1, \"div\", 32);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r3.request == null ? null : ctx_r3.request.detailedAddress) || (ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.detailedAddress));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.addressLink);\n  }\n}\nfunction RequestOverviewComponent_div_27_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"span\", 10);\n    i0.ɵɵtext(2, \"Mall Name:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 11);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.mallName);\n  }\n}\nfunction RequestOverviewComponent_div_27_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"span\", 10);\n    i0.ɵɵtext(2, \"Unit Number:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 11);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.unitNumber);\n  }\n}\nfunction RequestOverviewComponent_div_27_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"span\", 10);\n    i0.ɵɵtext(2, \"Unit View:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 11);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.unitView);\n  }\n}\nfunction RequestOverviewComponent_div_27_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"span\", 10);\n    i0.ɵɵtext(2, \"Floor:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 11);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.floor);\n  }\n}\nfunction RequestOverviewComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"h6\", 3)(2, \"i\", 34);\n    i0.ɵɵelement(3, \"span\", 5)(4, \"span\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Property Details \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 8);\n    i0.ɵɵtemplate(7, RequestOverviewComponent_div_27_div_7_Template, 5, 1, \"div\", 32)(8, RequestOverviewComponent_div_27_div_8_Template, 5, 1, \"div\", 32)(9, RequestOverviewComponent_div_27_div_9_Template, 5, 1, \"div\", 32)(10, RequestOverviewComponent_div_27_div_10_Template, 5, 1, \"div\", 32);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.mallName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.unitNumber);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.unitView);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.floor);\n  }\n}\nfunction RequestOverviewComponent_div_28_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"span\", 10);\n    i0.ɵɵtext(2, \"Delivery Status:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 11);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.deliveryStatus);\n  }\n}\nfunction RequestOverviewComponent_div_28_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"span\", 10);\n    i0.ɵɵtext(2, \"Financial Status:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 11);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.financialStatus);\n  }\n}\nfunction RequestOverviewComponent_div_28_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"span\", 10);\n    i0.ɵɵtext(2, \"Finishing Status:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 11);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.finishingStatus);\n  }\n}\nfunction RequestOverviewComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"h6\", 3)(2, \"i\", 35);\n    i0.ɵɵelement(3, \"span\", 5)(4, \"span\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Status Information \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 8);\n    i0.ɵɵtemplate(7, RequestOverviewComponent_div_28_div_7_Template, 5, 1, \"div\", 32)(8, RequestOverviewComponent_div_28_div_8_Template, 5, 1, \"div\", 32)(9, RequestOverviewComponent_div_28_div_9_Template, 5, 1, \"div\", 32);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.deliveryStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.financialStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.finishingStatus);\n  }\n}\nfunction RequestOverviewComponent_div_29_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r5, \" \");\n  }\n}\nfunction RequestOverviewComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"h6\", 3)(2, \"i\", 36);\n    i0.ɵɵelement(3, \"span\", 5)(4, \"span\", 6)(5, \"span\", 7)(6, \"span\", 15)(7, \"span\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" Additional Information \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 8)(10, \"span\", 10);\n    i0.ɵɵtext(11, \"Other Accessories:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 38);\n    i0.ɵɵtemplate(13, RequestOverviewComponent_div_29_span_13_Template, 2, 1, \"span\", 39);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.otherAccessories);\n  }\n}\nfunction RequestOverviewComponent_div_30_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"span\", 10);\n    i0.ɵɵtext(2, \"Payment Method:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 11);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.paymentMethod);\n  }\n}\nfunction RequestOverviewComponent_div_30_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"span\", 10);\n    i0.ɵɵtext(2, \"Unit Price:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 11);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.unitPrice);\n  }\n}\nfunction RequestOverviewComponent_div_30_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"span\", 10);\n    i0.ɵɵtext(2, \"Unit Price Suggestion:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 42);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(2, _c0, (ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.unitPriceSuggestions) === 1, (ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.unitPriceSuggestions) !== 1));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.unitPriceSuggestions) === 1 ? \"APPROVED\" : \"PENDING\", \" \");\n  }\n}\nfunction RequestOverviewComponent_div_30_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"span\", 10);\n    i0.ɵɵtext(2, \"Notes:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 11);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.notes);\n  }\n}\nfunction RequestOverviewComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"h6\", 3)(2, \"i\", 41);\n    i0.ɵɵelement(3, \"span\", 5)(4, \"span\", 6)(5, \"span\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" Financial & Notes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 8);\n    i0.ɵɵtemplate(8, RequestOverviewComponent_div_30_div_8_Template, 5, 1, \"div\", 32)(9, RequestOverviewComponent_div_30_div_9_Template, 5, 1, \"div\", 32)(10, RequestOverviewComponent_div_30_div_10_Template, 5, 5, \"div\", 32)(11, RequestOverviewComponent_div_30_div_11_Template, 5, 1, \"div\", 32);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.paymentMethod);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.unitPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.unitPriceSuggestions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.notes);\n  }\n}\nfunction RequestOverviewComponent_div_31_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"span\", 46)(2, \"i\", 47);\n    i0.ɵɵelement(3, \"span\", 5)(4, \"span\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Main Image : \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"img\", 48);\n    i0.ɵɵlistener(\"click\", function RequestOverviewComponent_div_31_div_7_Template_img_click_6_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.openImageModal(ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.mainImage, \"Main Image\"));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"src\", ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.mainImage, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction RequestOverviewComponent_div_31_div_8_img_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 52);\n    i0.ɵɵlistener(\"click\", function RequestOverviewComponent_div_31_div_8_img_7_Template_img_click_0_listener() {\n      const ctx_r7 = i0.ɵɵrestoreView(_r7);\n      const image_r9 = ctx_r7.$implicit;\n      const i_r10 = ctx_r7.index;\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.openImageModal(image_r9, \"Gallery Image \" + (i_r10 + 1)));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const image_r9 = ctx.$implicit;\n    const i_r10 = ctx.index;\n    i0.ɵɵproperty(\"src\", image_r9, i0.ɵɵsanitizeUrl)(\"alt\", \"Gallery Image \" + (i_r10 + 1));\n  }\n}\nfunction RequestOverviewComponent_div_31_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"span\", 46)(2, \"i\", 49);\n    i0.ɵɵelement(3, \"span\", 5)(4, \"span\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Gallery Images : \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 50);\n    i0.ɵɵtemplate(7, RequestOverviewComponent_div_31_div_8_img_7_Template, 1, 2, \"img\", 51);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.galleryImages);\n  }\n}\nfunction RequestOverviewComponent_div_31_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"span\", 46)(2, \"i\", 53);\n    i0.ɵɵelement(3, \"span\", 5)(4, \"span\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Unit Plan : \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"img\", 54);\n    i0.ɵɵlistener(\"click\", function RequestOverviewComponent_div_31_div_9_Template_img_click_6_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.openImageModal(ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.unitInMasterPlanImage, \"Unit in Master Plan\"));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"src\", ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.unitInMasterPlanImage, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction RequestOverviewComponent_div_31_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"span\", 46)(2, \"i\", 55);\n    i0.ɵɵelement(3, \"span\", 5)(4, \"span\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Video : \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 56);\n    i0.ɵɵlistener(\"click\", function RequestOverviewComponent_div_31_div_10_Template_div_click_6_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.openVideoModal(ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.video));\n    });\n    i0.ɵɵelementStart(7, \"i\", 57);\n    i0.ɵɵelement(8, \"span\", 5)(9, \"span\", 6);\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction RequestOverviewComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"h6\", 3)(2, \"i\", 43);\n    i0.ɵɵelement(3, \"span\", 5)(4, \"span\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Media Gallery \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 8);\n    i0.ɵɵtemplate(7, RequestOverviewComponent_div_31_div_7_Template, 7, 1, \"div\", 44)(8, RequestOverviewComponent_div_31_div_8_Template, 8, 1, \"div\", 44)(9, RequestOverviewComponent_div_31_div_9_Template, 7, 1, \"div\", 44)(10, RequestOverviewComponent_div_31_div_10_Template, 10, 0, \"div\", 44);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.mainImage);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.galleryImages == null ? null : ctx_r3.request.attributes.galleryImages.length) > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.unitInMasterPlanImage);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.request == null ? null : ctx_r3.request.attributes == null ? null : ctx_r3.request.attributes.video);\n  }\n}\nfunction RequestOverviewComponent_img_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 58);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r3.modalContent, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r3.modalTitle);\n  }\n}\nfunction RequestOverviewComponent_video_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"video\", 59);\n    i0.ɵɵtext(1, \" Your browser does not support the video tag. \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r3.modalContent, i0.ɵɵsanitizeUrl);\n  }\n}\nexport class RequestOverviewComponent {\n  cd;\n  requestService;\n  route;\n  request = null;\n  requestId = null;\n  routeSub = null;\n  requestSub = null;\n  // Modal properties\n  modalType = 'image';\n  modalContent = '';\n  modalTitle = '';\n  constructor(cd, requestService, route) {\n    this.cd = cd;\n    this.requestService = requestService;\n    this.route = route;\n  }\n  ngOnInit() {\n    if (this.route.parent) {\n      this.routeSub = this.route.parent.paramMap.subscribe(params => {\n        this.requestId = params.get('id') || this.requestService.getRequestId();\n        console.log('RequestOverviewComponent - Request ID:', this.requestId);\n        if (this.requestId) {\n          this.requestSub = this.requestService.getRequest().subscribe(request => {\n            this.request = request;\n            console.log('RequestOverviewComponent - Request Data from Service:', this.request);\n            this.cd.detectChanges();\n            if (!this.request) {\n              this.fetchRequest();\n            }\n          });\n        } else {\n          console.error('RequestOverviewComponent - No request ID found');\n          Swal.fire('Invalid request ID.', '', 'error');\n        }\n      });\n    } else {\n      // Handle case where parent route is not available\n      this.routeSub = null;\n      this.requestId = this.requestService.getRequestId();\n      console.error('RequestOverviewComponent - Parent route not found, fallback requestId:', this.requestId);\n      if (this.requestId) {\n        this.requestSub = this.requestService.getRequest().subscribe(request => {\n          this.request = request;\n          console.log('RequestOverviewComponent - Request Data from Service:', this.request);\n          this.cd.detectChanges();\n          if (!this.request) {\n            this.fetchRequest();\n          }\n        });\n      } else {\n        console.error('RequestOverviewComponent - No request ID available');\n        Swal.fire('Invalid request ID.', '', 'error');\n      }\n    }\n  }\n  ngOnDestroy() {\n    if (this.routeSub) {\n      this.routeSub.unsubscribe();\n    }\n    if (this.requestSub) {\n      this.requestSub.unsubscribe();\n    }\n  }\n  fetchRequest() {\n    if (this.requestId) {\n      this.requestService.getRequestById(this.requestId).subscribe({\n        next: response => {\n          this.request = response.data;\n          this.requestService.setRequest(this.request);\n          console.log('RequestOverviewComponent - Fetched Request Data:', this.request);\n          this.cd.detectChanges();\n        },\n        error: error => {\n          console.error('RequestOverviewComponent - Error fetching request:', error);\n          this.cd.detectChanges();\n          Swal.fire('Failed to load data. Please try again later.', '', 'error');\n        }\n      });\n    }\n  }\n  // Modal methods\n  openImageModal(imageUrl, title) {\n    this.modalType = 'image';\n    this.modalContent = imageUrl;\n    this.modalTitle = title;\n  }\n  openVideoModal(videoUrl) {\n    this.modalType = 'video';\n    this.modalContent = videoUrl;\n    this.modalTitle = 'Video';\n  }\n  static ɵfac = function RequestOverviewComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || RequestOverviewComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.RequestService), i0.ɵɵdirectiveInject(i2.ActivatedRoute));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: RequestOverviewComponent,\n    selectors: [[\"app-request-overview\"]],\n    decls: 58,\n    vars: 14,\n    consts: [[1, \"card\", \"border-0\", \"shadow-sm\", \"mb-5\"], [1, \"card-body\", \"p-4\"], [1, \"mb-5\"], [1, \"text-primary\", \"mb-3\", \"fw-bold\", \"bg-gray-100\", \"rounded\", \"p-3\"], [1, \"ki-duotone\", \"ki-information-5\", \"fs-6\", \"text-primary\", \"me-2\"], [1, \"path1\"], [1, \"path2\"], [1, \"path3\"], [1, \"rounded\", \"p-3\"], [1, \"mb-2\"], [1, \"text-gray-600\", \"fs-6\", \"fw-normal\"], [1, \"text-gray-800\", \"fs-5\", \"fw-semibold\", \"ms-2\"], [\"class\", \"mb-5\", 4, \"ngIf\"], [1, \"mb-4\"], [1, \"ki-duotone\", \"ki-calendar-2\", \"fs-6\", \"text-primary\", \"me-2\"], [1, \"path4\"], [\"id\", \"mediaModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"mediaModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\", \"modal-lg\", \"modal-dialog-centered\"], [1, \"modal-content\"], [1, \"modal-header\"], [\"id\", \"mediaModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"btn-close\"], [1, \"modal-body\", \"text-center\"], [\"class\", \"img-fluid rounded shadow\", 3, \"src\", \"alt\", 4, \"ngIf\"], [\"controls\", \"\", \"class\", \"w-100 rounded shadow\", \"style\", \"max-height: 500px;\", 3, \"src\", 4, \"ngIf\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [1, \"ki-duotone\", \"ki-geolocation\", \"fs-6\", \"text-primary\", \"me-2\"], [1, \"rounded\", \"p-3\", \"mb-3\"], [1, \"row\", \"g-3\"], [4, \"ngFor\", \"ngForOf\"], [1, \"ki-duotone\", \"ki-map\", \"fs-6\", \"text-primary\", \"me-2\"], [\"class\", \"mb-2\", 4, \"ngIf\"], [\"target\", \"_blank\", \"rel\", \"noopener noreferrer\", 1, \"text-primary\", \"fs-5\", \"fw-semibold\", \"ms-2\", \"text-hover-dark\", 3, \"href\"], [1, \"ki-duotone\", \"ki-home-2\", \"fs-6\", \"text-primary\", \"me-2\"], [1, \"ki-duotone\", \"ki-status\", \"fs-6\", \"text-primary\", \"me-2\"], [1, \"ki-duotone\", \"ki-element-plus\", \"fs-6\", \"text-primary\", \"me-2\"], [1, \"path5\"], [1, \"d-inline-flex\", \"flex-wrap\", \"gap-2\", \"ms-2\"], [\"class\", \"badge badge-light-primary fs-8 fw-semibold px-2 py-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"badge\", \"badge-light-primary\", \"fs-8\", \"fw-semibold\", \"px-2\", \"py-1\"], [1, \"ki-duotone\", \"ki-dollar\", \"fs-6\", \"text-primary\", \"me-2\"], [1, \"badge\", \"fs-6\", \"fw-semibold\", \"ms-2\", 3, \"ngClass\"], [1, \"ki-duotone\", \"ki-picture\", \"fs-6\", \"text-primary\", \"me-2\"], [\"class\", \"mb-2 d-flex align-items-center\", 4, \"ngIf\"], [1, \"mb-2\", \"d-flex\", \"align-items-center\"], [1, \"text-gray-600\", \"fs-6\", \"me-3\", 2, \"min-width\", \"120px\"], [1, \"ki-duotone\", \"ki-picture\", \"fs-6\", \"text-primary\", \"me-1\"], [\"alt\", \"Main Image\", \"data-bs-toggle\", \"modal\", \"data-bs-target\", \"#mediaModal\", 1, \"rounded\", \"cursor-pointer\", \"shadow-sm\", 2, \"height\", \"60px\", \"width\", \"80px\", \"object-fit\", \"cover\", 3, \"click\", \"src\"], [1, \"ki-duotone\", \"ki-gallery\", \"fs-6\", \"text-info\", \"me-1\"], [1, \"d-flex\", \"gap-2\"], [\"class\", \"rounded cursor-pointer shadow-sm\", \"style\", \"height: 60px; width: 80px; object-fit: cover;\", \"data-bs-toggle\", \"modal\", \"data-bs-target\", \"#mediaModal\", 3, \"src\", \"alt\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"data-bs-toggle\", \"modal\", \"data-bs-target\", \"#mediaModal\", 1, \"rounded\", \"cursor-pointer\", \"shadow-sm\", 2, \"height\", \"60px\", \"width\", \"80px\", \"object-fit\", \"cover\", 3, \"click\", \"src\", \"alt\"], [1, \"ki-duotone\", \"ki-design-frame\", \"fs-6\", \"text-success\", \"me-1\"], [\"alt\", \"Unit in Master Plan\", \"data-bs-toggle\", \"modal\", \"data-bs-target\", \"#mediaModal\", 1, \"rounded\", \"cursor-pointer\", \"shadow-sm\", 2, \"height\", \"60px\", \"width\", \"80px\", \"object-fit\", \"cover\", 3, \"click\", \"src\"], [1, \"ki-duotone\", \"ki-video\", \"fs-6\", \"text-danger\", \"me-1\"], [\"data-bs-toggle\", \"modal\", \"data-bs-target\", \"#mediaModal\", 1, \"bg-dark\", \"rounded\", \"d-flex\", \"align-items-center\", \"justify-content-center\", \"cursor-pointer\", \"shadow-sm\", \"position-relative\", 2, \"height\", \"60px\", \"width\", \"80px\", 3, \"click\"], [1, \"ki-duotone\", \"ki-play-circle\", \"fs-2\", \"text-white\"], [1, \"img-fluid\", \"rounded\", \"shadow\", 3, \"src\", \"alt\"], [\"controls\", \"\", 1, \"w-100\", \"rounded\", \"shadow\", 2, \"max-height\", \"500px\", 3, \"src\"]],\n    template: function RequestOverviewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h6\", 3)(4, \"i\", 4);\n        i0.ɵɵelement(5, \"span\", 5)(6, \"span\", 6)(7, \"span\", 7);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(8, \" Basic Information \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"div\", 8)(10, \"div\", 9)(11, \"span\", 10);\n        i0.ɵɵtext(12, \"Operation Type:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(13, \"span\", 11);\n        i0.ɵɵtext(14);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(15, \"div\", 9)(16, \"span\", 10);\n        i0.ɵɵtext(17, \"Specialization:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(18, \"span\", 11);\n        i0.ɵɵtext(19);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(20, \"div\", 9)(21, \"span\", 10);\n        i0.ɵɵtext(22, \"Unit Type:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(23, \"span\", 11);\n        i0.ɵɵtext(24);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵtemplate(25, RequestOverviewComponent_div_25_Template, 9, 1, \"div\", 12)(26, RequestOverviewComponent_div_26_Template, 10, 2, \"div\", 12)(27, RequestOverviewComponent_div_27_Template, 11, 4, \"div\", 12)(28, RequestOverviewComponent_div_28_Template, 10, 3, \"div\", 12)(29, RequestOverviewComponent_div_29_Template, 14, 1, \"div\", 12)(30, RequestOverviewComponent_div_30_Template, 12, 4, \"div\", 12)(31, RequestOverviewComponent_div_31_Template, 11, 4, \"div\", 12);\n        i0.ɵɵelementStart(32, \"div\", 13)(33, \"h6\", 3)(34, \"i\", 14);\n        i0.ɵɵelement(35, \"span\", 5)(36, \"span\", 6)(37, \"span\", 7)(38, \"span\", 15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(39, \" Request Information \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(40, \"div\", 8)(41, \"span\", 10);\n        i0.ɵɵtext(42, \"Created At:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(43, \"span\", 11);\n        i0.ɵɵtext(44);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(45, \"div\", 16)(46, \"div\", 17)(47, \"div\", 18)(48, \"div\", 19)(49, \"h5\", 20);\n        i0.ɵɵtext(50);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(51, \"button\", 21);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(52, \"div\", 22);\n        i0.ɵɵtemplate(53, RequestOverviewComponent_img_53_Template, 1, 2, \"img\", 23)(54, RequestOverviewComponent_video_54_Template, 2, 1, \"video\", 24);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(55, \"div\", 25)(56, \"button\", 26);\n        i0.ɵɵtext(57, \"Close\");\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(14);\n        i0.ɵɵtextInterpolate(ctx.request == null ? null : ctx.request.type);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(ctx.request == null ? null : ctx.request.specializationScope);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(ctx.request == null ? null : ctx.request.unit);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", (ctx.request == null ? null : ctx.request.locations) && (ctx.request == null ? null : ctx.request.locations.length) > 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", (ctx.request == null ? null : ctx.request.detailedAddress) || (ctx.request == null ? null : ctx.request.attributes == null ? null : ctx.request.attributes.detailedAddress) || (ctx.request == null ? null : ctx.request.attributes == null ? null : ctx.request.attributes.addressLink));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", (ctx.request == null ? null : ctx.request.attributes == null ? null : ctx.request.attributes.mallName) || (ctx.request == null ? null : ctx.request.attributes == null ? null : ctx.request.attributes.unitNumber) || (ctx.request == null ? null : ctx.request.attributes == null ? null : ctx.request.attributes.unitView) || (ctx.request == null ? null : ctx.request.attributes == null ? null : ctx.request.attributes.floor));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", (ctx.request == null ? null : ctx.request.attributes == null ? null : ctx.request.attributes.deliveryStatus) || (ctx.request == null ? null : ctx.request.attributes == null ? null : ctx.request.attributes.financialStatus) || (ctx.request == null ? null : ctx.request.attributes == null ? null : ctx.request.attributes.finishingStatus));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.request == null ? null : ctx.request.attributes == null ? null : ctx.request.attributes.otherAccessories);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", (ctx.request == null ? null : ctx.request.attributes == null ? null : ctx.request.attributes.notes) || (ctx.request == null ? null : ctx.request.attributes == null ? null : ctx.request.attributes.paymentMethod) || (ctx.request == null ? null : ctx.request.attributes == null ? null : ctx.request.attributes.unitPrice) || (ctx.request == null ? null : ctx.request.attributes == null ? null : ctx.request.attributes.unitPriceSuggestions));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", (ctx.request == null ? null : ctx.request.attributes == null ? null : ctx.request.attributes.mainImage) || (ctx.request == null ? null : ctx.request.attributes == null ? null : ctx.request.attributes.galleryImages == null ? null : ctx.request.attributes.galleryImages.length) > 0 || (ctx.request == null ? null : ctx.request.attributes == null ? null : ctx.request.attributes.unitInMasterPlanImage) || (ctx.request == null ? null : ctx.request.attributes == null ? null : ctx.request.attributes.video));\n        i0.ɵɵadvance(13);\n        i0.ɵɵtextInterpolate(ctx.request == null ? null : ctx.request.createdAt);\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate(ctx.modalTitle);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.modalType === \"image\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.modalType === \"video\");\n      }\n    },\n    dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf],\n    styles: [\".cursor-pointer[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: transform 0.2s ease-in-out;\\n}\\n.cursor-pointer[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n\\n.position-relative[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.position-relative[_ngcontent-%COMP%]   .position-absolute[_ngcontent-%COMP%] {\\n  position: absolute;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvcmVxdWVzdHMvY29tcG9uZW50cy9yZW5kZXItcmVxdWVzdC9yZXF1ZXN0LW92ZXJ2aWV3L3JlcXVlc3Qtb3ZlcnZpZXcuY29tcG9uZW50LnNjc3MiLCJ3ZWJwYWNrOi8vLi8uLi8uLi8uLi8uLi8uLi9jb21wdSUyMHpvbmUvRGVza3RvcC90YXNrZXMvTmV3JTIwZm9sZGVyL2Vhc3lkZWFsLWZyb250ZW5kL3NyYy9hcHAvcGFnZXMvcmVxdWVzdHMvY29tcG9uZW50cy9yZW5kZXItcmVxdWVzdC9yZXF1ZXN0LW92ZXJ2aWV3L3JlcXVlc3Qtb3ZlcnZpZXcuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxlQUFBO0VBQ0Esc0NBQUE7QUNDRjtBRENFO0VBQ0Usc0JBQUE7QUNDSjs7QURHQTtFQUNFLGtCQUFBO0FDQUY7QURFRTtFQUNFLGtCQUFBO0FDQUoiLCJzb3VyY2VzQ29udGVudCI6WyIuY3Vyc29yLXBvaW50ZXIge1xyXG4gIGN1cnNvcjogcG9pbnRlcjtcclxuICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4ycyBlYXNlLWluLW91dDtcclxuXHJcbiAgJjpob3ZlciB7XHJcbiAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMDUpO1xyXG4gIH1cclxufVxyXG5cclxuLnBvc2l0aW9uLXJlbGF0aXZlIHtcclxuICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcblxyXG4gIC5wb3NpdGlvbi1hYnNvbHV0ZSB7XHJcbiAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgfVxyXG59XHJcbiIsIi5jdXJzb3ItcG9pbnRlciB7XG4gIGN1cnNvcjogcG9pbnRlcjtcbiAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuMnMgZWFzZS1pbi1vdXQ7XG59XG4uY3Vyc29yLXBvaW50ZXI6aG92ZXIge1xuICB0cmFuc2Zvcm06IHNjYWxlKDEuMDUpO1xufVxuXG4ucG9zaXRpb24tcmVsYXRpdmUge1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG59XG4ucG9zaXRpb24tcmVsYXRpdmUgLnBvc2l0aW9uLWFic29sdXRlIHtcbiAgcG9zaXRpb246IGFic29sdXRlO1xufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "names": ["<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate2", "cityItem_r3", "city", "name_en", "name_ar", "areaItem_r2", "area", "subarea_r1", "ɵɵtemplate", "RequestOverviewComponent_div_25_div_8_div_1_div_1_Template", "ɵɵproperty", "sub_areas", "RequestOverviewComponent_div_25_div_8_div_1_Template", "areas", "ɵɵelement", "RequestOverviewComponent_div_25_div_8_Template", "ctx_r3", "request", "locations", "ɵɵtextInterpolate", "detailed<PERSON>ddress", "attributes", "addressLink", "ɵɵsanitizeUrl", "RequestOverviewComponent_div_26_div_8_Template", "RequestOverviewComponent_div_26_div_9_Template", "mallName", "unitNumber", "unitView", "floor", "RequestOverviewComponent_div_27_div_7_Template", "RequestOverviewComponent_div_27_div_8_Template", "RequestOverviewComponent_div_27_div_9_Template", "RequestOverviewComponent_div_27_div_10_Template", "deliveryStatus", "financialStatus", "finishingStatus", "RequestOverviewComponent_div_28_div_7_Template", "RequestOverviewComponent_div_28_div_8_Template", "RequestOverviewComponent_div_28_div_9_Template", "ɵɵtextInterpolate1", "item_r5", "RequestOverviewComponent_div_29_span_13_Template", "otherAccessories", "paymentMethod", "unitPrice", "ɵɵpureFunction2", "_c0", "unitPriceSuggestions", "notes", "RequestOverviewComponent_div_30_div_8_Template", "RequestOverviewComponent_div_30_div_9_Template", "RequestOverviewComponent_div_30_div_10_Template", "RequestOverviewComponent_div_30_div_11_Template", "ɵɵlistener", "RequestOverviewComponent_div_31_div_7_Template_img_click_6_listener", "ɵɵrestoreView", "_r6", "ɵɵnextContext", "ɵɵresetView", "openImageModal", "mainImage", "RequestOverviewComponent_div_31_div_8_img_7_Template_img_click_0_listener", "ctx_r7", "_r7", "image_r9", "$implicit", "i_r10", "index", "RequestOverviewComponent_div_31_div_8_img_7_Template", "galleryImages", "RequestOverviewComponent_div_31_div_9_Template_img_click_6_listener", "_r11", "unitInMasterPlanImage", "RequestOverviewComponent_div_31_div_10_Template_div_click_6_listener", "_r12", "openVideoModal", "video", "RequestOverviewComponent_div_31_div_7_Template", "RequestOverviewComponent_div_31_div_8_Template", "RequestOverviewComponent_div_31_div_9_Template", "RequestOverviewComponent_div_31_div_10_Template", "length", "modalContent", "modalTitle", "RequestOverviewComponent", "cd", "requestService", "route", "requestId", "routeSub", "requestSub", "modalType", "constructor", "ngOnInit", "parent", "paramMap", "subscribe", "params", "get", "getRequestId", "console", "log", "getRequest", "detectChanges", "fetchRequest", "error", "fire", "ngOnDestroy", "unsubscribe", "getRequestById", "next", "response", "data", "setRequest", "imageUrl", "title", "videoUrl", "ɵɵdirectiveInject", "ChangeDetectorRef", "i1", "RequestService", "i2", "ActivatedRoute", "selectors", "decls", "vars", "consts", "template", "RequestOverviewComponent_Template", "rf", "ctx", "RequestOverviewComponent_div_25_Template", "RequestOverviewComponent_div_26_Template", "RequestOverviewComponent_div_27_Template", "RequestOverviewComponent_div_28_Template", "RequestOverviewComponent_div_29_Template", "RequestOverviewComponent_div_30_Template", "RequestOverviewComponent_div_31_Template", "RequestOverviewComponent_img_53_Template", "RequestOverviewComponent_video_54_Template", "type", "specializationScope", "unit", "createdAt"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\requests\\components\\render-request\\request-overview\\request-overview.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\requests\\components\\render-request\\request-overview\\request-overview.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';\r\nimport { RequestService } from '../../../services/request.service';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { Subscription } from 'rxjs';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'app-request-overview',\r\n  templateUrl: './request-overview.component.html',\r\n  styleUrls: ['./request-overview.component.scss'],\r\n})\r\nexport class RequestOverviewComponent implements OnInit, OnDestroy {\r\n  request: any = null;\r\n  requestId: string | null = null;\r\n  private routeSub: Subscription | null = null;\r\n  private requestSub: Subscription | null = null;\r\n\r\n  // Modal properties\r\n  modalType: 'image' | 'video' = 'image';\r\n  modalContent: string = '';\r\n  modalTitle: string = '';\r\n\r\n  constructor(\r\n    protected cd: ChangeDetectorRef,\r\n    protected requestService: RequestService,\r\n    private route: ActivatedRoute\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n     if (this.route.parent) {\r\n      this.routeSub = this.route.parent.paramMap.subscribe((params) => {\r\n        this.requestId = params.get('id') || this.requestService.getRequestId();\r\n        console.log('RequestOverviewComponent - Request ID:', this.requestId);\r\n\r\n        if (this.requestId) {\r\n           this.requestSub = this.requestService.getRequest().subscribe((request) => {\r\n            this.request = request;\r\n            console.log('RequestOverviewComponent - Request Data from Service:', this.request);\r\n            this.cd.detectChanges();\r\n\r\n             if (!this.request) {\r\n              this.fetchRequest();\r\n            }\r\n          });\r\n        } else {\r\n          console.error('RequestOverviewComponent - No request ID found');\r\n          Swal.fire('Invalid request ID.', '', 'error');\r\n        }\r\n      });\r\n    } else {\r\n      // Handle case where parent route is not available\r\n      this.routeSub = null;\r\n      this.requestId = this.requestService.getRequestId();\r\n      console.error('RequestOverviewComponent - Parent route not found, fallback requestId:', this.requestId);\r\n      if (this.requestId) {\r\n        this.requestSub = this.requestService.getRequest().subscribe((request) => {\r\n          this.request = request;\r\n          console.log('RequestOverviewComponent - Request Data from Service:', this.request);\r\n          this.cd.detectChanges();\r\n          if (!this.request) {\r\n            this.fetchRequest();\r\n          }\r\n        });\r\n      } else {\r\n        console.error('RequestOverviewComponent - No request ID available');\r\n        Swal.fire('Invalid request ID.', '', 'error');\r\n      }\r\n    }\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    if (this.routeSub) {\r\n      this.routeSub.unsubscribe();\r\n    }\r\n    if (this.requestSub) {\r\n      this.requestSub.unsubscribe();\r\n    }\r\n  }\r\n\r\n  fetchRequest() {\r\n    if (this.requestId) {\r\n      this.requestService.getRequestById(this.requestId).subscribe({\r\n        next: (response: any) => {\r\n          this.request = response.data;\r\n          this.requestService.setRequest(this.request);\r\n          console.log('RequestOverviewComponent - Fetched Request Data:', this.request);\r\n          this.cd.detectChanges();\r\n        },\r\n        error: (error: any) => {\r\n          console.error('RequestOverviewComponent - Error fetching request:', error);\r\n          this.cd.detectChanges();\r\n          Swal.fire('Failed to load data. Please try again later.', '', 'error');\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  // Modal methods\r\n  openImageModal(imageUrl: string, title: string) {\r\n    this.modalType = 'image';\r\n    this.modalContent = imageUrl;\r\n    this.modalTitle = title;\r\n  }\r\n\r\n  openVideoModal(videoUrl: string) {\r\n    this.modalType = 'video';\r\n    this.modalContent = videoUrl;\r\n    this.modalTitle = 'Video';\r\n  }\r\n}\r\n", "<!-- Simple Request Overview -->\r\n<div class=\"card border-0 shadow-sm mb-5\">\r\n  <!-- Simple Header -->\r\n  <!-- <div class=\"card-header bg-light py-3 border-bottom\">\r\n    <h6 class=\"text-gray-800 mb-0 fw-bold\">\r\n      <i class=\"ki-duotone ki-document-text fs-6 text-primary me-2\">\r\n        <span class=\"path1\"></span>\r\n        <span class=\"path2\"></span>\r\n      </i>\r\n      Request Overview\r\n    </h6>\r\n  </div> -->\r\n\r\n  <div class=\"card-body p-4\">\r\n    <!-- Basic Information -->\r\n    <div class=\"mb-5 \">\r\n      <h6 class=\"text-primary mb-3 fw-bold bg-gray-100 rounded p-3\">\r\n        <i class=\"ki-duotone ki-information-5 fs-6 text-primary me-2\">\r\n          <span class=\"path1\"></span>\r\n          <span class=\"path2\"></span>\r\n          <span class=\"path3\"></span>\r\n        </i>\r\n        Basic Information\r\n      </h6>\r\n\r\n      <div class=\"rounded p-3\">\r\n        <div class=\"mb-2\">\r\n          <span class=\"text-gray-600 fs-6 fw-normal\">Operation Type:</span>\r\n          <span class=\"text-gray-800 fs-5 fw-semibold ms-2\">{{ request?.type }}</span>\r\n        </div>\r\n        <div class=\"mb-2\">\r\n          <span class=\"text-gray-600 fs-6 fw-normal\">Specialization:</span>\r\n          <span class=\"text-gray-800 fs-5 fw-semibold ms-2\">{{ request?.specializationScope }}</span>\r\n        </div>\r\n        <div class=\"mb-2\">\r\n          <span class=\"text-gray-600 fs-6 fw-normal\">Unit Type:</span>\r\n          <span class=\"text-gray-800 fs-5 fw-semibold ms-2\">{{ request?.unit }}</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Location Information -->\r\n    <div class=\"mb-5\" *ngIf=\"request?.locations && request?.locations.length > 0\">\r\n      <h6 class=\"text-primary mb-3 fw-bold bg-gray-100 rounded p-3\">\r\n        <i class=\"ki-duotone ki-geolocation fs-6 text-primary me-2\">\r\n          <span class=\"path1\"></span>\r\n          <span class=\"path2\"></span>\r\n        </i>\r\n        Location Details\r\n      </h6>\r\n\r\n      <div class=\"rounded p-3 mb-3\">\r\n        <div class=\"row g-3\">\r\n          <div *ngFor=\"let cityItem of request?.locations\">\r\n            <div *ngFor=\"let areaItem of cityItem.areas\">\r\n              <div *ngFor=\"let subarea of areaItem.sub_areas\">\r\n                <!-- City -->\r\n                <div class=\"mb-2\">\r\n                  <span class=\"text-gray-600 fs-6 fw-normal\">City:</span>\r\n                  <span class=\"text-gray-800 fs-5 fw-semibold ms-2\">{{ cityItem.city.name_en }} | {{\r\n                    cityItem.city.name_ar }}</span>\r\n                </div>\r\n\r\n                <!-- Area -->\r\n                <div class=\"mb-2\">\r\n                  <span class=\"text-gray-600 fs-6 fw-normal\">Area:</span>\r\n                  <span class=\"text-gray-800 fs-5 fw-semibold ms-2\">{{ areaItem.area.name_en }} | {{\r\n                    areaItem.area.name_ar }}</span>\r\n                </div>\r\n\r\n                <!-- Subarea -->\r\n                <div class=\"mb-2\">\r\n                  <span class=\"text-gray-600 fs-6 fw-normal\">Subarea:</span>\r\n                  <span class=\"text-gray-800 fs-5 fw-semibold ms-2\">{{ subarea.name_en }} | {{ subarea.name_ar }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n    </div>\r\n\r\n    <!-- Additional Address Info -->\r\n    <div class=\"mb-5\"\r\n      *ngIf=\"request?.detailedAddress || request?.attributes?.detailedAddress || request?.attributes?.addressLink\">\r\n      <h6 class=\"text-primary mb-3 fw-bold bg-gray-100 rounded p-3\">\r\n        <i class=\"ki-duotone ki-map fs-6 text-primary me-2\">\r\n          <span class=\"path1\"></span>\r\n          <span class=\"path2\"></span>\r\n          <span class=\"path3\"></span>\r\n        </i>\r\n        Address Information\r\n      </h6>\r\n\r\n      <div class=\"rounded p-3\">\r\n        <div *ngIf=\"request?.detailedAddress || request?.attributes?.detailedAddress\" class=\"mb-2\">\r\n          <span class=\"text-gray-600 fs-6 fw-normal\">Detailed Address:</span>\r\n          <span class=\"text-gray-800 fs-5 fw-semibold ms-2\">{{ request?.detailedAddress ||\r\n            request?.attributes?.detailedAddress }}</span>\r\n        </div>\r\n\r\n        <div *ngIf=\"request?.attributes?.addressLink\" class=\"mb-2\">\r\n          <span class=\"text-gray-600 fs-6 fw-normal\">Address Link:</span>\r\n          <a [href]=\"request.attributes.addressLink\" target=\"_blank\" rel=\"noopener noreferrer\"\r\n            class=\"text-primary fs-5 fw-semibold ms-2 text-hover-dark\">View on Map</a>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Property Details -->\r\n    <div class=\"mb-5\"\r\n      *ngIf=\"request?.attributes?.mallName || request?.attributes?.unitNumber || request?.attributes?.unitView || request?.attributes?.floor\">\r\n      <h6 class=\"text-primary mb-3 fw-bold bg-gray-100 rounded p-3\">\r\n        <i class=\"ki-duotone ki-home-2 fs-6 text-primary me-2\">\r\n          <span class=\"path1\"></span>\r\n          <span class=\"path2\"></span>\r\n        </i>\r\n        Property Details\r\n      </h6>\r\n\r\n      <div class=\"rounded p-3\">\r\n        <div *ngIf=\"request?.attributes?.mallName\" class=\"mb-2\">\r\n          <span class=\"text-gray-600 fs-6 fw-normal\">Mall Name:</span>\r\n          <span class=\"text-gray-800 fs-5 fw-semibold ms-2\">{{ request?.attributes?.mallName }}</span>\r\n        </div>\r\n        <div *ngIf=\"request?.attributes?.unitNumber\" class=\"mb-2\">\r\n          <span class=\"text-gray-600 fs-6 fw-normal\">Unit Number:</span>\r\n          <span class=\"text-gray-800 fs-5 fw-semibold ms-2\">{{ request?.attributes?.unitNumber }}</span>\r\n        </div>\r\n        <div *ngIf=\"request?.attributes?.unitView\" class=\"mb-2\">\r\n          <span class=\"text-gray-600 fs-6 fw-normal\">Unit View:</span>\r\n          <span class=\"text-gray-800 fs-5 fw-semibold ms-2\">{{ request?.attributes?.unitView }}</span>\r\n        </div>\r\n        <div *ngIf=\"request?.attributes?.floor\" class=\"mb-2\">\r\n          <span class=\"text-gray-600 fs-6 fw-normal\">Floor:</span>\r\n          <span class=\"text-gray-800 fs-5 fw-semibold ms-2\">{{ request?.attributes?.floor }}</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Status Information -->\r\n    <div class=\"mb-5\"\r\n      *ngIf=\"request?.attributes?.deliveryStatus || request?.attributes?.financialStatus || request?.attributes?.finishingStatus\">\r\n      <h6 class=\"text-primary mb-3 fw-bold bg-gray-100 rounded p-3\">\r\n        <i class=\"ki-duotone ki-status fs-6 text-primary me-2\">\r\n          <span class=\"path1\"></span>\r\n          <span class=\"path2\"></span>\r\n        </i>\r\n        Status Information\r\n      </h6>\r\n\r\n      <div class=\"rounded p-3\">\r\n        <div *ngIf=\"request?.attributes?.deliveryStatus\" class=\"mb-2\">\r\n          <span class=\"text-gray-600 fs-6 fw-normal\">Delivery Status:</span>\r\n          <span class=\"text-gray-800 fs-5 fw-semibold ms-2\">{{ request?.attributes?.deliveryStatus }}</span>\r\n        </div>\r\n        <div *ngIf=\"request?.attributes?.financialStatus\" class=\"mb-2\">\r\n          <span class=\"text-gray-600 fs-6 fw-normal\">Financial Status:</span>\r\n          <span class=\"text-gray-800 fs-5 fw-semibold ms-2\">{{ request?.attributes?.financialStatus }}</span>\r\n        </div>\r\n        <div *ngIf=\"request?.attributes?.finishingStatus\" class=\"mb-2\">\r\n          <span class=\"text-gray-600 fs-6 fw-normal\">Finishing Status:</span>\r\n          <span class=\"text-gray-800 fs-5 fw-semibold ms-2\">{{ request?.attributes?.finishingStatus }}</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Additional Information -->\r\n    <div class=\"mb-5\" *ngIf=\"request?.attributes?.otherAccessories\">\r\n      <h6 class=\"text-primary mb-3 fw-bold bg-gray-100 rounded p-3\">\r\n        <i class=\"ki-duotone ki-element-plus fs-6 text-primary me-2\">\r\n          <span class=\"path1\"></span>\r\n          <span class=\"path2\"></span>\r\n          <span class=\"path3\"></span>\r\n          <span class=\"path4\"></span>\r\n          <span class=\"path5\"></span>\r\n        </i>\r\n        Additional Information\r\n      </h6>\r\n\r\n      <div class=\"rounded p-3\">\r\n        <span class=\"text-gray-600 fs-6 fw-normal\">Other Accessories:</span>\r\n        <div class=\"d-inline-flex flex-wrap gap-2 ms-2\">\r\n          <span *ngFor=\"let item of request?.attributes?.otherAccessories\"\r\n            class=\"badge badge-light-primary fs-8 fw-semibold px-2 py-1\">\r\n            {{ item }}\r\n          </span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Notes & Financial Information -->\r\n    <div class=\"mb-5\"\r\n      *ngIf=\"request?.attributes?.notes || request?.attributes?.paymentMethod || request?.attributes?.unitPrice || request?.attributes?.unitPriceSuggestions\">\r\n      <h6 class=\"text-primary mb-3 fw-bold bg-gray-100 rounded p-3\">\r\n        <i class=\"ki-duotone ki-dollar fs-6 text-primary me-2\">\r\n          <span class=\"path1\"></span>\r\n          <span class=\"path2\"></span>\r\n          <span class=\"path3\"></span>\r\n        </i>\r\n        Financial & Notes\r\n      </h6>\r\n\r\n      <div class=\"rounded p-3\">\r\n        <div *ngIf=\"request?.attributes?.paymentMethod\" class=\"mb-2\">\r\n          <span class=\"text-gray-600 fs-6 fw-normal\">Payment Method:</span>\r\n          <span class=\"text-gray-800 fs-5 fw-semibold ms-2\">{{ request?.attributes?.paymentMethod }}</span>\r\n        </div>\r\n        <div *ngIf=\"request?.attributes?.unitPrice\" class=\"mb-2\">\r\n          <span class=\"text-gray-600 fs-6 fw-normal\">Unit Price:</span>\r\n          <span class=\"text-gray-800 fs-5 fw-semibold ms-2\">{{ request?.attributes?.unitPrice }}</span>\r\n        </div>\r\n        <div *ngIf=\"request?.attributes?.unitPriceSuggestions\" class=\"mb-2\">\r\n          <span class=\"text-gray-600 fs-6 fw-normal\">Unit Price Suggestion:</span>\r\n          <span class=\"badge fs-6 fw-semibold ms-2\" [ngClass]=\"{\r\n            'badge-light-success': request?.attributes?.unitPriceSuggestions === 1,\r\n            'badge-light-danger': request?.attributes?.unitPriceSuggestions !== 1\r\n          }\">\r\n            {{ (request?.attributes?.unitPriceSuggestions === 1 ? 'APPROVED' : 'PENDING') }}\r\n          </span>\r\n        </div>\r\n        <div *ngIf=\"request?.attributes?.notes\" class=\"mb-2\">\r\n          <span class=\"text-gray-600 fs-6 fw-normal\">Notes:</span>\r\n          <span class=\"text-gray-800 fs-5 fw-semibold ms-2\">{{ request?.attributes?.notes }}</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Media Gallery -->\r\n    <div class=\"mb-5\"\r\n      *ngIf=\"request?.attributes?.mainImage || request?.attributes?.galleryImages?.length > 0 || request?.attributes?.unitInMasterPlanImage || request?.attributes?.video\">\r\n      <h6 class=\"text-primary mb-3 fw-bold bg-gray-100 rounded p-3\">\r\n        <i class=\"ki-duotone ki-picture fs-6 text-primary me-2\">\r\n          <span class=\"path1\"></span>\r\n          <span class=\"path2\"></span>\r\n        </i>\r\n        Media Gallery\r\n      </h6>\r\n\r\n      <div class=\"rounded p-3\">\r\n        <!-- Main Image -->\r\n        <div class=\"mb-2 d-flex align-items-center\" *ngIf=\"request?.attributes?.mainImage\">\r\n          <span class=\"text-gray-600 fs-6 me-3\" style=\"min-width: 120px;\">\r\n            <i class=\"ki-duotone ki-picture fs-6 text-primary me-1\">\r\n              <span class=\"path1\"></span>\r\n              <span class=\"path2\"></span>\r\n            </i>\r\n            Main Image :\r\n          </span>\r\n          <img [src]=\"request?.attributes?.mainImage\" alt=\"Main Image\" class=\"rounded cursor-pointer shadow-sm\"\r\n            style=\"height: 60px; width: 80px; object-fit: cover;\"\r\n            (click)=\"openImageModal(request?.attributes?.mainImage, 'Main Image')\" data-bs-toggle=\"modal\"\r\n            data-bs-target=\"#mediaModal\">\r\n        </div>\r\n\r\n        <!-- Gallery Images -->\r\n        <div class=\"mb-2 d-flex align-items-center\" *ngIf=\"request?.attributes?.galleryImages?.length > 0\">\r\n          <span class=\"text-gray-600 fs-6 me-3\" style=\"min-width: 120px;\">\r\n            <i class=\"ki-duotone ki-gallery fs-6 text-info me-1\">\r\n              <span class=\"path1\"></span>\r\n              <span class=\"path2\"></span>\r\n            </i>\r\n            Gallery Images :\r\n          </span>\r\n          <div class=\"d-flex gap-2\">\r\n            <img *ngFor=\"let image of request?.attributes?.galleryImages; let i = index\" [src]=\"image\"\r\n              [alt]=\"'Gallery Image ' + (i + 1)\" class=\"rounded cursor-pointer shadow-sm\"\r\n              style=\"height: 60px; width: 80px; object-fit: cover;\"\r\n              (click)=\"openImageModal(image, 'Gallery Image ' + (i + 1))\" data-bs-toggle=\"modal\"\r\n              data-bs-target=\"#mediaModal\">\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Unit in Master Plan Image -->\r\n        <div class=\"mb-2 d-flex align-items-center\" *ngIf=\"request?.attributes?.unitInMasterPlanImage\">\r\n          <span class=\"text-gray-600 fs-6 me-3\" style=\"min-width: 120px;\">\r\n            <i class=\"ki-duotone ki-design-frame fs-6 text-success me-1\">\r\n              <span class=\"path1\"></span>\r\n              <span class=\"path2\"></span>\r\n            </i>\r\n            Unit Plan :\r\n          </span>\r\n          <img [src]=\"request?.attributes?.unitInMasterPlanImage\" alt=\"Unit in Master Plan\"\r\n            class=\"rounded cursor-pointer shadow-sm\" style=\"height: 60px; width: 80px; object-fit: cover;\"\r\n            (click)=\"openImageModal(request?.attributes?.unitInMasterPlanImage, 'Unit in Master Plan')\"\r\n            data-bs-toggle=\"modal\" data-bs-target=\"#mediaModal\">\r\n        </div>\r\n\r\n        <!-- Video -->\r\n        <div class=\"mb-2 d-flex align-items-center\" *ngIf=\"request?.attributes?.video\">\r\n          <span class=\"text-gray-600 fs-6 me-3\" style=\"min-width: 120px;\">\r\n            <i class=\"ki-duotone ki-video fs-6 text-danger me-1\">\r\n              <span class=\"path1\"></span>\r\n              <span class=\"path2\"></span>\r\n            </i>\r\n            Video :\r\n          </span>\r\n          <div\r\n            class=\"bg-dark rounded d-flex align-items-center justify-content-center cursor-pointer shadow-sm position-relative\"\r\n            style=\"height: 60px; width: 80px;\" (click)=\"openVideoModal(request?.attributes?.video)\"\r\n            data-bs-toggle=\"modal\" data-bs-target=\"#mediaModal\">\r\n            <i class=\"ki-duotone ki-play-circle fs-2 text-white\">\r\n              <span class=\"path1\"></span>\r\n              <span class=\"path2\"></span>\r\n            </i>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Created At -->\r\n    <div class=\"mb-4\">\r\n      <h6 class=\"text-primary mb-3 fw-bold bg-gray-100 rounded p-3\">\r\n        <i class=\"ki-duotone ki-calendar-2 fs-6 text-primary me-2\">\r\n          <span class=\"path1\"></span>\r\n          <span class=\"path2\"></span>\r\n          <span class=\"path3\"></span>\r\n          <span class=\"path4\"></span>\r\n        </i>\r\n        Request Information\r\n      </h6>\r\n\r\n      <div class=\"rounded p-3\">\r\n        <span class=\"text-gray-600 fs-6 fw-normal\">Created At:</span>\r\n        <span class=\"text-gray-800 fs-5 fw-semibold ms-2\">{{ request?.createdAt }}</span>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<!-- Media Modal -->\r\n<div class=\"modal fade\" id=\"mediaModal\" tabindex=\"-1\" aria-labelledby=\"mediaModalLabel\" aria-hidden=\"true\">\r\n  <div class=\"modal-dialog modal-lg modal-dialog-centered\">\r\n    <div class=\"modal-content\">\r\n      <div class=\"modal-header\">\r\n        <h5 class=\"modal-title\" id=\"mediaModalLabel\">{{ modalTitle }}</h5>\r\n        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>\r\n      </div>\r\n      <div class=\"modal-body text-center\">\r\n        <!-- Image Display -->\r\n        <img *ngIf=\"modalType === 'image'\" [src]=\"modalContent\" [alt]=\"modalTitle\" class=\"img-fluid rounded shadow\">\r\n\r\n        <!-- Video Display -->\r\n        <video *ngIf=\"modalType === 'video'\" [src]=\"modalContent\" controls class=\"w-100 rounded shadow\"\r\n          style=\"max-height: 500px;\">\r\n          Your browser does not support the video tag.\r\n        </video>\r\n      </div>\r\n      <div class=\"modal-footer\">\r\n        <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Close</button>\r\n\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAIA,OAAOA,IAAI,MAAM,aAAa;;;;;;;;;;;ICsDZC,EAHJ,CAAAC,cAAA,UAAgD,aAE5B,eAC2B;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvDH,EAAA,CAAAC,cAAA,eAAkD;IAAAD,EAAA,CAAAE,MAAA,GACxB;IAC5BF,EAD4B,CAAAG,YAAA,EAAO,EAC7B;IAIJH,EADF,CAAAC,cAAA,aAAkB,eAC2B;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvDH,EAAA,CAAAC,cAAA,eAAkD;IAAAD,EAAA,CAAAE,MAAA,IACxB;IAC5BF,EAD4B,CAAAG,YAAA,EAAO,EAC7B;IAIJH,EADF,CAAAC,cAAA,cAAkB,gBAC2B;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1DH,EAAA,CAAAC,cAAA,gBAAkD;IAAAD,EAAA,CAAAE,MAAA,IAA6C;IAEnGF,EAFmG,CAAAG,YAAA,EAAO,EAClG,EACF;;;;;;IAhBgDH,EAAA,CAAAI,SAAA,GACxB;IADwBJ,EAAA,CAAAK,kBAAA,KAAAC,WAAA,CAAAC,IAAA,CAAAC,OAAA,SAAAF,WAAA,CAAAC,IAAA,CAAAE,OAAA,KACxB;IAMwBT,EAAA,CAAAI,SAAA,GACxB;IADwBJ,EAAA,CAAAK,kBAAA,KAAAK,WAAA,CAAAC,IAAA,CAAAH,OAAA,SAAAE,WAAA,CAAAC,IAAA,CAAAF,OAAA,KACxB;IAMwBT,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAK,kBAAA,KAAAO,UAAA,CAAAJ,OAAA,SAAAI,UAAA,CAAAH,OAAA,KAA6C;;;;;IAnBrGT,EAAA,CAAAC,cAAA,UAA6C;IAC3CD,EAAA,CAAAa,UAAA,IAAAC,0DAAA,mBAAgD;IAqBlDd,EAAA,CAAAG,YAAA,EAAM;;;;IArBqBH,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAe,UAAA,YAAAL,WAAA,CAAAM,SAAA,CAAqB;;;;;IAFlDhB,EAAA,CAAAC,cAAA,UAAiD;IAC/CD,EAAA,CAAAa,UAAA,IAAAI,oDAAA,kBAA6C;IAuB/CjB,EAAA,CAAAG,YAAA,EAAM;;;;IAvBsBH,EAAA,CAAAI,SAAA,EAAiB;IAAjBJ,EAAA,CAAAe,UAAA,YAAAT,WAAA,CAAAY,KAAA,CAAiB;;;;;IAV/ClB,EAFJ,CAAAC,cAAA,aAA8E,YACd,YACA;IAE1DD,EADA,CAAAmB,SAAA,cAA2B,cACA;IAC7BnB,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAE,MAAA,yBACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGHH,EADF,CAAAC,cAAA,cAA8B,cACP;IACnBD,EAAA,CAAAa,UAAA,IAAAO,8CAAA,kBAAiD;IA4BvDpB,EAHI,CAAAG,YAAA,EAAM,EACF,EAEF;;;;IA5B0BH,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAe,UAAA,YAAAM,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAC,SAAA,CAAqB;;;;;IA4C/CvB,EADF,CAAAC,cAAA,aAA2F,eAC9C;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAC,cAAA,eAAkD;IAAAD,EAAA,CAAAE,MAAA,GACT;IAC3CF,EAD2C,CAAAG,YAAA,EAAO,EAC5C;;;;IAF8CH,EAAA,CAAAI,SAAA,GACT;IADSJ,EAAA,CAAAwB,iBAAA,EAAAH,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAG,eAAA,MAAAJ,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAAD,eAAA,EACT;;;;;IAIzCzB,EADF,CAAAC,cAAA,aAA2D,eACd;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/DH,EAAA,CAAAC,cAAA,YAC6D;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAC1EF,EAD0E,CAAAG,YAAA,EAAI,EACxE;;;;IAFDH,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAe,UAAA,SAAAM,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAAC,WAAA,EAAA3B,EAAA,CAAA4B,aAAA,CAAuC;;;;;IAjB5C5B,EAHJ,CAAAC,cAAA,aAC+G,YAC/C,YACR;IAGlDD,EAFA,CAAAmB,SAAA,cAA2B,cACA,cACA;IAC7BnB,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,aAAyB;IAOvBD,EANA,CAAAa,UAAA,IAAAgB,8CAAA,kBAA2F,IAAAC,8CAAA,kBAMhC;IAM/D9B,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAZIH,EAAA,CAAAI,SAAA,GAAsE;IAAtEJ,EAAA,CAAAe,UAAA,UAAAM,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAG,eAAA,MAAAJ,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAAD,eAAA,EAAsE;IAMtEzB,EAAA,CAAAI,SAAA,EAAsC;IAAtCJ,EAAA,CAAAe,UAAA,SAAAM,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAAC,WAAA,CAAsC;;;;;IAqB1C3B,EADF,CAAAC,cAAA,aAAwD,eACX;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5DH,EAAA,CAAAC,cAAA,eAAkD;IAAAD,EAAA,CAAAE,MAAA,GAAmC;IACvFF,EADuF,CAAAG,YAAA,EAAO,EACxF;;;;IAD8CH,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAwB,iBAAA,CAAAH,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAAK,QAAA,CAAmC;;;;;IAGrF/B,EADF,CAAAC,cAAA,aAA0D,eACb;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC9DH,EAAA,CAAAC,cAAA,eAAkD;IAAAD,EAAA,CAAAE,MAAA,GAAqC;IACzFF,EADyF,CAAAG,YAAA,EAAO,EAC1F;;;;IAD8CH,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAwB,iBAAA,CAAAH,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAAM,UAAA,CAAqC;;;;;IAGvFhC,EADF,CAAAC,cAAA,aAAwD,eACX;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5DH,EAAA,CAAAC,cAAA,eAAkD;IAAAD,EAAA,CAAAE,MAAA,GAAmC;IACvFF,EADuF,CAAAG,YAAA,EAAO,EACxF;;;;IAD8CH,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAwB,iBAAA,CAAAH,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAAO,QAAA,CAAmC;;;;;IAGrFjC,EADF,CAAAC,cAAA,aAAqD,eACR;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxDH,EAAA,CAAAC,cAAA,eAAkD;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IACpFF,EADoF,CAAAG,YAAA,EAAO,EACrF;;;;IAD8CH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAwB,iBAAA,CAAAH,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAAQ,KAAA,CAAgC;;;;;IAtBpFlC,EAHJ,CAAAC,cAAA,aAC0I,YAC1E,YACL;IAErDD,EADA,CAAAmB,SAAA,cAA2B,cACA;IAC7BnB,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAE,MAAA,yBACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,aAAyB;IAavBD,EAZA,CAAAa,UAAA,IAAAsB,8CAAA,kBAAwD,IAAAC,8CAAA,kBAIE,IAAAC,8CAAA,kBAIF,KAAAC,+CAAA,kBAIH;IAKzDtC,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAjBIH,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAe,UAAA,SAAAM,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAAK,QAAA,CAAmC;IAInC/B,EAAA,CAAAI,SAAA,EAAqC;IAArCJ,EAAA,CAAAe,UAAA,SAAAM,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAAM,UAAA,CAAqC;IAIrChC,EAAA,CAAAI,SAAA,EAAmC;IAAnCJ,EAAA,CAAAe,UAAA,SAAAM,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAAO,QAAA,CAAmC;IAInCjC,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAe,UAAA,SAAAM,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAAQ,KAAA,CAAgC;;;;;IAoBpClC,EADF,CAAAC,cAAA,aAA8D,eACjB;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClEH,EAAA,CAAAC,cAAA,eAAkD;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAC7FF,EAD6F,CAAAG,YAAA,EAAO,EAC9F;;;;IAD8CH,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAwB,iBAAA,CAAAH,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAAa,cAAA,CAAyC;;;;;IAG3FvC,EADF,CAAAC,cAAA,aAA+D,eAClB;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAC,cAAA,eAAkD;IAAAD,EAAA,CAAAE,MAAA,GAA0C;IAC9FF,EAD8F,CAAAG,YAAA,EAAO,EAC/F;;;;IAD8CH,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAwB,iBAAA,CAAAH,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAAc,eAAA,CAA0C;;;;;IAG5FxC,EADF,CAAAC,cAAA,aAA+D,eAClB;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAC,cAAA,eAAkD;IAAAD,EAAA,CAAAE,MAAA,GAA0C;IAC9FF,EAD8F,CAAAG,YAAA,EAAO,EAC/F;;;;IAD8CH,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAwB,iBAAA,CAAAH,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAAe,eAAA,CAA0C;;;;;IAlB9FzC,EAHJ,CAAAC,cAAA,aAC8H,YAC9D,YACL;IAErDD,EADA,CAAAmB,SAAA,cAA2B,cACA;IAC7BnB,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,aAAyB;IASvBD,EARA,CAAAa,UAAA,IAAA6B,8CAAA,kBAA8D,IAAAC,8CAAA,kBAIC,IAAAC,8CAAA,kBAIA;IAKnE5C,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAbIH,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAe,UAAA,SAAAM,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAAa,cAAA,CAAyC;IAIzCvC,EAAA,CAAAI,SAAA,EAA0C;IAA1CJ,EAAA,CAAAe,UAAA,SAAAM,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAAc,eAAA,CAA0C;IAI1CxC,EAAA,CAAAI,SAAA,EAA0C;IAA1CJ,EAAA,CAAAe,UAAA,SAAAM,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAAe,eAAA,CAA0C;;;;;IAuB9CzC,EAAA,CAAAC,cAAA,eAC+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAA6C,kBAAA,MAAAC,OAAA,MACF;;;;;IAhBF9C,EAFJ,CAAAC,cAAA,aAAgE,YACA,YACC;IAK3DD,EAJA,CAAAmB,SAAA,cAA2B,cACA,cACA,eACA,eACA;IAC7BnB,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAE,MAAA,+BACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGHH,EADF,CAAAC,cAAA,aAAyB,gBACoB;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpEH,EAAA,CAAAC,cAAA,eAAgD;IAC9CD,EAAA,CAAAa,UAAA,KAAAkC,gDAAA,mBAC+D;IAKrE/C,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IANuBH,EAAA,CAAAI,SAAA,IAAwC;IAAxCJ,EAAA,CAAAe,UAAA,YAAAM,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAAsB,gBAAA,CAAwC;;;;;IAsB/DhD,EADF,CAAAC,cAAA,aAA6D,eAChB;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjEH,EAAA,CAAAC,cAAA,eAAkD;IAAAD,EAAA,CAAAE,MAAA,GAAwC;IAC5FF,EAD4F,CAAAG,YAAA,EAAO,EAC7F;;;;IAD8CH,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAwB,iBAAA,CAAAH,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAAuB,aAAA,CAAwC;;;;;IAG1FjD,EADF,CAAAC,cAAA,aAAyD,eACZ;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7DH,EAAA,CAAAC,cAAA,eAAkD;IAAAD,EAAA,CAAAE,MAAA,GAAoC;IACxFF,EADwF,CAAAG,YAAA,EAAO,EACzF;;;;IAD8CH,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAwB,iBAAA,CAAAH,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAAwB,SAAA,CAAoC;;;;;IAGtFlD,EADF,CAAAC,cAAA,aAAoE,eACvB;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxEH,EAAA,CAAAC,cAAA,eAGG;IACDD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;;;;IANsCH,EAAA,CAAAI,SAAA,GAGxC;IAHwCJ,EAAA,CAAAe,UAAA,YAAAf,EAAA,CAAAmD,eAAA,IAAAC,GAAA,GAAA/B,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAA2B,oBAAA,UAAAhC,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAA2B,oBAAA,SAGxC;IACArD,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAA6C,kBAAA,OAAAxB,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAA2B,oBAAA,sCACF;;;;;IAGArD,EADF,CAAAC,cAAA,aAAqD,eACR;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxDH,EAAA,CAAAC,cAAA,eAAkD;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IACpFF,EADoF,CAAAG,YAAA,EAAO,EACrF;;;;IAD8CH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAwB,iBAAA,CAAAH,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAA4B,KAAA,CAAgC;;;;;IA5BpFtD,EAHJ,CAAAC,cAAA,aAC0J,YAC1F,YACL;IAGrDD,EAFA,CAAAmB,SAAA,cAA2B,cACA,cACA;IAC7BnB,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,aAAyB;IAkBvBD,EAjBA,CAAAa,UAAA,IAAA0C,8CAAA,kBAA6D,IAAAC,8CAAA,kBAIJ,KAAAC,+CAAA,kBAIW,KAAAC,+CAAA,kBASf;IAKzD1D,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAtBIH,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAe,UAAA,SAAAM,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAAuB,aAAA,CAAwC;IAIxCjD,EAAA,CAAAI,SAAA,EAAoC;IAApCJ,EAAA,CAAAe,UAAA,SAAAM,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAAwB,SAAA,CAAoC;IAIpClD,EAAA,CAAAI,SAAA,EAA+C;IAA/CJ,EAAA,CAAAe,UAAA,SAAAM,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAA2B,oBAAA,CAA+C;IAS/CrD,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAe,UAAA,SAAAM,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAA4B,KAAA,CAAgC;;;;;;IAsBlCtD,EAFJ,CAAAC,cAAA,cAAmF,eACjB,YACN;IAEtDD,EADA,CAAAmB,SAAA,cAA2B,cACA;IAC7BnB,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,cAG+B;IAD7BD,EAAA,CAAA2D,UAAA,mBAAAC,oEAAA;MAAA5D,EAAA,CAAA6D,aAAA,CAAAC,GAAA;MAAA,MAAAzC,MAAA,GAAArB,EAAA,CAAA+D,aAAA;MAAA,OAAA/D,EAAA,CAAAgE,WAAA,CAAS3C,MAAA,CAAA4C,cAAA,CAAA5C,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAAwC,SAAA,EAA+C,YAAY,CAAC;IAAA,EAAC;IAE1ElE,EAJE,CAAAG,YAAA,EAG+B,EAC3B;;;;IAJCH,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAe,UAAA,QAAAM,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAAwC,SAAA,EAAAlE,EAAA,CAAA4B,aAAA,CAAsC;;;;;;IAgBzC5B,EAAA,CAAAC,cAAA,cAI+B;IAD7BD,EAAA,CAAA2D,UAAA,mBAAAQ,0EAAA;MAAA,MAAAC,MAAA,GAAApE,EAAA,CAAA6D,aAAA,CAAAQ,GAAA;MAAA,MAAAC,QAAA,GAAAF,MAAA,CAAAG,SAAA;MAAA,MAAAC,KAAA,GAAAJ,MAAA,CAAAK,KAAA;MAAA,MAAApD,MAAA,GAAArB,EAAA,CAAA+D,aAAA;MAAA,OAAA/D,EAAA,CAAAgE,WAAA,CAAS3C,MAAA,CAAA4C,cAAA,CAAAK,QAAA,EAAsB,gBAAgB,IAAAE,KAAA,GAAQ,CAAC,EAAE;IAAA,EAAC;IAH7DxE,EAAA,CAAAG,YAAA,EAI+B;;;;;IAH7BH,EAD2E,CAAAe,UAAA,QAAAuD,QAAA,EAAAtE,EAAA,CAAA4B,aAAA,CAAa,4BAAA4C,KAAA,MACtD;;;;;IARpCxE,EAFJ,CAAAC,cAAA,cAAmG,eACjC,YACT;IAEnDD,EADA,CAAAmB,SAAA,cAA2B,cACA;IAC7BnB,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAE,MAAA,yBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAa,UAAA,IAAA6D,oDAAA,kBAI+B;IAEnC1E,EADE,CAAAG,YAAA,EAAM,EACF;;;;IANqBH,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAe,UAAA,YAAAM,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAAiD,aAAA,CAAuC;;;;;;IAW9D3E,EAFJ,CAAAC,cAAA,cAA+F,eAC7B,YACD;IAE3DD,EADA,CAAAmB,SAAA,cAA2B,cACA;IAC7BnB,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAE,MAAA,oBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,cAGsD;IADpDD,EAAA,CAAA2D,UAAA,mBAAAiB,oEAAA;MAAA5E,EAAA,CAAA6D,aAAA,CAAAgB,IAAA;MAAA,MAAAxD,MAAA,GAAArB,EAAA,CAAA+D,aAAA;MAAA,OAAA/D,EAAA,CAAAgE,WAAA,CAAS3C,MAAA,CAAA4C,cAAA,CAAA5C,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAAoD,qBAAA,EAA2D,qBAAqB,CAAC;IAAA,EAAC;IAE/F9E,EAJE,CAAAG,YAAA,EAGsD,EAClD;;;;IAJCH,EAAA,CAAAI,SAAA,GAAkD;IAAlDJ,EAAA,CAAAe,UAAA,QAAAM,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAAoD,qBAAA,EAAA9E,EAAA,CAAA4B,aAAA,CAAkD;;;;;;IASrD5B,EAFJ,CAAAC,cAAA,cAA+E,eACb,YACT;IAEnDD,EADA,CAAAmB,SAAA,cAA2B,cACA;IAC7BnB,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,cAGsD;IADjBD,EAAA,CAAA2D,UAAA,mBAAAoB,qEAAA;MAAA/E,EAAA,CAAA6D,aAAA,CAAAmB,IAAA;MAAA,MAAA3D,MAAA,GAAArB,EAAA,CAAA+D,aAAA;MAAA,OAAA/D,EAAA,CAAAgE,WAAA,CAAS3C,MAAA,CAAA4D,cAAA,CAAA5D,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAAwD,KAAA,CAA0C;IAAA,EAAC;IAEvFlF,EAAA,CAAAC,cAAA,YAAqD;IAEnDD,EADA,CAAAmB,SAAA,cAA2B,cACA;IAGjCnB,EAFI,CAAAG,YAAA,EAAI,EACA,EACF;;;;;IA1ENH,EAHJ,CAAAC,cAAA,aACuK,YACvG,YACJ;IAEtDD,EADA,CAAAmB,SAAA,cAA2B,cACA;IAC7BnB,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,aAAyB;IAkDvBD,EAhDA,CAAAa,UAAA,IAAAsE,8CAAA,kBAAmF,IAAAC,8CAAA,kBAegB,IAAAC,8CAAA,kBAkBJ,KAAAC,+CAAA,mBAehB;IAmBnFtF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAnE2CH,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAe,UAAA,SAAAM,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAAwC,SAAA,CAAoC;IAepClE,EAAA,CAAAI,SAAA,EAAoD;IAApDJ,EAAA,CAAAe,UAAA,UAAAM,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAAiD,aAAA,kBAAAtD,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAAiD,aAAA,CAAAY,MAAA,MAAoD;IAkBpDvF,EAAA,CAAAI,SAAA,EAAgD;IAAhDJ,EAAA,CAAAe,UAAA,SAAAM,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAAoD,qBAAA,CAAgD;IAehD9E,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAe,UAAA,SAAAM,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAI,UAAA,kBAAAL,MAAA,CAAAC,OAAA,CAAAI,UAAA,CAAAwD,KAAA,CAAgC;;;;;IAmD7ElF,EAAA,CAAAmB,SAAA,cAA4G;;;;IAApDnB,EAArB,CAAAe,UAAA,QAAAM,MAAA,CAAAmE,YAAA,EAAAxF,EAAA,CAAA4B,aAAA,CAAoB,QAAAP,MAAA,CAAAoE,UAAA,CAAmB;;;;;IAG1EzF,EAAA,CAAAC,cAAA,gBAC6B;IAC3BD,EAAA,CAAAE,MAAA,qDACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAH6BH,EAAA,CAAAe,UAAA,QAAAM,MAAA,CAAAmE,YAAA,EAAAxF,EAAA,CAAA4B,aAAA,CAAoB;;;AD7UjE,OAAM,MAAO8D,wBAAwB;EAYvBC,EAAA;EACAC,cAAA;EACFC,KAAA;EAbVvE,OAAO,GAAQ,IAAI;EACnBwE,SAAS,GAAkB,IAAI;EACvBC,QAAQ,GAAwB,IAAI;EACpCC,UAAU,GAAwB,IAAI;EAE9C;EACAC,SAAS,GAAsB,OAAO;EACtCT,YAAY,GAAW,EAAE;EACzBC,UAAU,GAAW,EAAE;EAEvBS,YACYP,EAAqB,EACrBC,cAA8B,EAChCC,KAAqB;IAFnB,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,cAAc,GAAdA,cAAc;IAChB,KAAAC,KAAK,GAALA,KAAK;EACZ;EAEHM,QAAQA,CAAA;IACL,IAAI,IAAI,CAACN,KAAK,CAACO,MAAM,EAAE;MACtB,IAAI,CAACL,QAAQ,GAAG,IAAI,CAACF,KAAK,CAACO,MAAM,CAACC,QAAQ,CAACC,SAAS,CAAEC,MAAM,IAAI;QAC9D,IAAI,CAACT,SAAS,GAAGS,MAAM,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,CAACZ,cAAc,CAACa,YAAY,EAAE;QACvEC,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE,IAAI,CAACb,SAAS,CAAC;QAErE,IAAI,IAAI,CAACA,SAAS,EAAE;UACjB,IAAI,CAACE,UAAU,GAAG,IAAI,CAACJ,cAAc,CAACgB,UAAU,EAAE,CAACN,SAAS,CAAEhF,OAAO,IAAI;YACxE,IAAI,CAACA,OAAO,GAAGA,OAAO;YACtBoF,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAE,IAAI,CAACrF,OAAO,CAAC;YAClF,IAAI,CAACqE,EAAE,CAACkB,aAAa,EAAE;YAEtB,IAAI,CAAC,IAAI,CAACvF,OAAO,EAAE;cAClB,IAAI,CAACwF,YAAY,EAAE;YACrB;UACF,CAAC,CAAC;QACJ,CAAC,MAAM;UACLJ,OAAO,CAACK,KAAK,CAAC,gDAAgD,CAAC;UAC/DhH,IAAI,CAACiH,IAAI,CAAC,qBAAqB,EAAE,EAAE,EAAE,OAAO,CAAC;QAC/C;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,IAAI,CAACjB,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACD,SAAS,GAAG,IAAI,CAACF,cAAc,CAACa,YAAY,EAAE;MACnDC,OAAO,CAACK,KAAK,CAAC,wEAAwE,EAAE,IAAI,CAACjB,SAAS,CAAC;MACvG,IAAI,IAAI,CAACA,SAAS,EAAE;QAClB,IAAI,CAACE,UAAU,GAAG,IAAI,CAACJ,cAAc,CAACgB,UAAU,EAAE,CAACN,SAAS,CAAEhF,OAAO,IAAI;UACvE,IAAI,CAACA,OAAO,GAAGA,OAAO;UACtBoF,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAE,IAAI,CAACrF,OAAO,CAAC;UAClF,IAAI,CAACqE,EAAE,CAACkB,aAAa,EAAE;UACvB,IAAI,CAAC,IAAI,CAACvF,OAAO,EAAE;YACjB,IAAI,CAACwF,YAAY,EAAE;UACrB;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACLJ,OAAO,CAACK,KAAK,CAAC,oDAAoD,CAAC;QACnEhH,IAAI,CAACiH,IAAI,CAAC,qBAAqB,EAAE,EAAE,EAAE,OAAO,CAAC;MAC/C;IACF;EACF;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAAClB,QAAQ,EAAE;MACjB,IAAI,CAACA,QAAQ,CAACmB,WAAW,EAAE;IAC7B;IACA,IAAI,IAAI,CAAClB,UAAU,EAAE;MACnB,IAAI,CAACA,UAAU,CAACkB,WAAW,EAAE;IAC/B;EACF;EAEAJ,YAAYA,CAAA;IACV,IAAI,IAAI,CAAChB,SAAS,EAAE;MAClB,IAAI,CAACF,cAAc,CAACuB,cAAc,CAAC,IAAI,CAACrB,SAAS,CAAC,CAACQ,SAAS,CAAC;QAC3Dc,IAAI,EAAGC,QAAa,IAAI;UACtB,IAAI,CAAC/F,OAAO,GAAG+F,QAAQ,CAACC,IAAI;UAC5B,IAAI,CAAC1B,cAAc,CAAC2B,UAAU,CAAC,IAAI,CAACjG,OAAO,CAAC;UAC5CoF,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAE,IAAI,CAACrF,OAAO,CAAC;UAC7E,IAAI,CAACqE,EAAE,CAACkB,aAAa,EAAE;QACzB,CAAC;QACDE,KAAK,EAAGA,KAAU,IAAI;UACpBL,OAAO,CAACK,KAAK,CAAC,oDAAoD,EAAEA,KAAK,CAAC;UAC1E,IAAI,CAACpB,EAAE,CAACkB,aAAa,EAAE;UACvB9G,IAAI,CAACiH,IAAI,CAAC,8CAA8C,EAAE,EAAE,EAAE,OAAO,CAAC;QACxE;OACD,CAAC;IACJ;EACF;EAEA;EACA/C,cAAcA,CAACuD,QAAgB,EAAEC,KAAa;IAC5C,IAAI,CAACxB,SAAS,GAAG,OAAO;IACxB,IAAI,CAACT,YAAY,GAAGgC,QAAQ;IAC5B,IAAI,CAAC/B,UAAU,GAAGgC,KAAK;EACzB;EAEAxC,cAAcA,CAACyC,QAAgB;IAC7B,IAAI,CAACzB,SAAS,GAAG,OAAO;IACxB,IAAI,CAACT,YAAY,GAAGkC,QAAQ;IAC5B,IAAI,CAACjC,UAAU,GAAG,OAAO;EAC3B;;qCAjGWC,wBAAwB,EAAA1F,EAAA,CAAA2H,iBAAA,CAAA3H,EAAA,CAAA4H,iBAAA,GAAA5H,EAAA,CAAA2H,iBAAA,CAAAE,EAAA,CAAAC,cAAA,GAAA9H,EAAA,CAAA2H,iBAAA,CAAAI,EAAA,CAAAC,cAAA;EAAA;;UAAxBtC,wBAAwB;IAAAuC,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCM7BvI,EAhBR,CAAAC,cAAA,aAA0C,aAYb,aAEN,YAC6C,WACE;QAG5DD,EAFA,CAAAmB,SAAA,cAA2B,cACA,cACA;QAC7BnB,EAAA,CAAAG,YAAA,EAAI;QACJH,EAAA,CAAAE,MAAA,0BACF;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAIDH,EAFJ,CAAAC,cAAA,aAAyB,cACL,gBAC2B;QAAAD,EAAA,CAAAE,MAAA,uBAAe;QAAAF,EAAA,CAAAG,YAAA,EAAO;QACjEH,EAAA,CAAAC,cAAA,gBAAkD;QAAAD,EAAA,CAAAE,MAAA,IAAmB;QACvEF,EADuE,CAAAG,YAAA,EAAO,EACxE;QAEJH,EADF,CAAAC,cAAA,cAAkB,gBAC2B;QAAAD,EAAA,CAAAE,MAAA,uBAAe;QAAAF,EAAA,CAAAG,YAAA,EAAO;QACjEH,EAAA,CAAAC,cAAA,gBAAkD;QAAAD,EAAA,CAAAE,MAAA,IAAkC;QACtFF,EADsF,CAAAG,YAAA,EAAO,EACvF;QAEJH,EADF,CAAAC,cAAA,cAAkB,gBAC2B;QAAAD,EAAA,CAAAE,MAAA,kBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAC5DH,EAAA,CAAAC,cAAA,gBAAkD;QAAAD,EAAA,CAAAE,MAAA,IAAmB;QAG3EF,EAH2E,CAAAG,YAAA,EAAO,EACxE,EACF,EACF;QA+LNH,EA5LA,CAAAa,UAAA,KAAA4H,wCAAA,kBAA8E,KAAAC,wCAAA,mBA2CiC,KAAAC,wCAAA,mBA2B2B,KAAAC,wCAAA,mBA+BZ,KAAAC,wCAAA,mBA0B9D,KAAAC,wCAAA,mBAyB0F,KAAAC,wCAAA,mBAqCa;QAmFnK/I,EAFJ,CAAAC,cAAA,eAAkB,aAC8C,aACD;QAIzDD,EAHA,CAAAmB,SAAA,eAA2B,eACA,eACA,gBACA;QAC7BnB,EAAA,CAAAG,YAAA,EAAI;QACJH,EAAA,CAAAE,MAAA,6BACF;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAGHH,EADF,CAAAC,cAAA,cAAyB,gBACoB;QAAAD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAC7DH,EAAA,CAAAC,cAAA,gBAAkD;QAAAD,EAAA,CAAAE,MAAA,IAAwB;QAIlFF,EAJkF,CAAAG,YAAA,EAAO,EAC7E,EACF,EACF,EACF;QAOEH,EAJR,CAAAC,cAAA,eAA2G,eAChD,eAC5B,eACC,cACqB;QAAAD,EAAA,CAAAE,MAAA,IAAgB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAClEH,EAAA,CAAAmB,SAAA,kBAA4F;QAC9FnB,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,eAAoC;QAKlCD,EAHA,CAAAa,UAAA,KAAAmI,wCAAA,kBAA4G,KAAAC,0CAAA,oBAI/E;QAG/BjJ,EAAA,CAAAG,YAAA,EAAM;QAEJH,EADF,CAAAC,cAAA,eAA0B,kBACgD;QAAAD,EAAA,CAAAE,MAAA,aAAK;QAKrFF,EALqF,CAAAG,YAAA,EAAS,EAElF,EACF,EACF,EACF;;;QAvUsDH,EAAA,CAAAI,SAAA,IAAmB;QAAnBJ,EAAA,CAAAwB,iBAAA,CAAAgH,GAAA,CAAAlH,OAAA,kBAAAkH,GAAA,CAAAlH,OAAA,CAAA4H,IAAA,CAAmB;QAInBlJ,EAAA,CAAAI,SAAA,GAAkC;QAAlCJ,EAAA,CAAAwB,iBAAA,CAAAgH,GAAA,CAAAlH,OAAA,kBAAAkH,GAAA,CAAAlH,OAAA,CAAA6H,mBAAA,CAAkC;QAIlCnJ,EAAA,CAAAI,SAAA,GAAmB;QAAnBJ,EAAA,CAAAwB,iBAAA,CAAAgH,GAAA,CAAAlH,OAAA,kBAAAkH,GAAA,CAAAlH,OAAA,CAAA8H,IAAA,CAAmB;QAMxDpJ,EAAA,CAAAI,SAAA,EAAyD;QAAzDJ,EAAA,CAAAe,UAAA,UAAAyH,GAAA,CAAAlH,OAAA,kBAAAkH,GAAA,CAAAlH,OAAA,CAAAC,SAAA,MAAAiH,GAAA,CAAAlH,OAAA,kBAAAkH,GAAA,CAAAlH,OAAA,CAAAC,SAAA,CAAAgE,MAAA,MAAyD;QA2CzEvF,EAAA,CAAAI,SAAA,EAA0G;QAA1GJ,EAAA,CAAAe,UAAA,UAAAyH,GAAA,CAAAlH,OAAA,kBAAAkH,GAAA,CAAAlH,OAAA,CAAAG,eAAA,MAAA+G,GAAA,CAAAlH,OAAA,kBAAAkH,GAAA,CAAAlH,OAAA,CAAAI,UAAA,kBAAA8G,GAAA,CAAAlH,OAAA,CAAAI,UAAA,CAAAD,eAAA,MAAA+G,GAAA,CAAAlH,OAAA,kBAAAkH,GAAA,CAAAlH,OAAA,CAAAI,UAAA,kBAAA8G,GAAA,CAAAlH,OAAA,CAAAI,UAAA,CAAAC,WAAA,EAA0G;QA2B1G3B,EAAA,CAAAI,SAAA,EAAqI;QAArIJ,EAAA,CAAAe,UAAA,UAAAyH,GAAA,CAAAlH,OAAA,kBAAAkH,GAAA,CAAAlH,OAAA,CAAAI,UAAA,kBAAA8G,GAAA,CAAAlH,OAAA,CAAAI,UAAA,CAAAK,QAAA,MAAAyG,GAAA,CAAAlH,OAAA,kBAAAkH,GAAA,CAAAlH,OAAA,CAAAI,UAAA,kBAAA8G,GAAA,CAAAlH,OAAA,CAAAI,UAAA,CAAAM,UAAA,MAAAwG,GAAA,CAAAlH,OAAA,kBAAAkH,GAAA,CAAAlH,OAAA,CAAAI,UAAA,kBAAA8G,GAAA,CAAAlH,OAAA,CAAAI,UAAA,CAAAO,QAAA,MAAAuG,GAAA,CAAAlH,OAAA,kBAAAkH,GAAA,CAAAlH,OAAA,CAAAI,UAAA,kBAAA8G,GAAA,CAAAlH,OAAA,CAAAI,UAAA,CAAAQ,KAAA,EAAqI;QA+BrIlC,EAAA,CAAAI,SAAA,EAAyH;QAAzHJ,EAAA,CAAAe,UAAA,UAAAyH,GAAA,CAAAlH,OAAA,kBAAAkH,GAAA,CAAAlH,OAAA,CAAAI,UAAA,kBAAA8G,GAAA,CAAAlH,OAAA,CAAAI,UAAA,CAAAa,cAAA,MAAAiG,GAAA,CAAAlH,OAAA,kBAAAkH,GAAA,CAAAlH,OAAA,CAAAI,UAAA,kBAAA8G,GAAA,CAAAlH,OAAA,CAAAI,UAAA,CAAAc,eAAA,MAAAgG,GAAA,CAAAlH,OAAA,kBAAAkH,GAAA,CAAAlH,OAAA,CAAAI,UAAA,kBAAA8G,GAAA,CAAAlH,OAAA,CAAAI,UAAA,CAAAe,eAAA,EAAyH;QA0BzGzC,EAAA,CAAAI,SAAA,EAA2C;QAA3CJ,EAAA,CAAAe,UAAA,SAAAyH,GAAA,CAAAlH,OAAA,kBAAAkH,GAAA,CAAAlH,OAAA,CAAAI,UAAA,kBAAA8G,GAAA,CAAAlH,OAAA,CAAAI,UAAA,CAAAsB,gBAAA,CAA2C;QAyB3DhD,EAAA,CAAAI,SAAA,EAAqJ;QAArJJ,EAAA,CAAAe,UAAA,UAAAyH,GAAA,CAAAlH,OAAA,kBAAAkH,GAAA,CAAAlH,OAAA,CAAAI,UAAA,kBAAA8G,GAAA,CAAAlH,OAAA,CAAAI,UAAA,CAAA4B,KAAA,MAAAkF,GAAA,CAAAlH,OAAA,kBAAAkH,GAAA,CAAAlH,OAAA,CAAAI,UAAA,kBAAA8G,GAAA,CAAAlH,OAAA,CAAAI,UAAA,CAAAuB,aAAA,MAAAuF,GAAA,CAAAlH,OAAA,kBAAAkH,GAAA,CAAAlH,OAAA,CAAAI,UAAA,kBAAA8G,GAAA,CAAAlH,OAAA,CAAAI,UAAA,CAAAwB,SAAA,MAAAsF,GAAA,CAAAlH,OAAA,kBAAAkH,GAAA,CAAAlH,OAAA,CAAAI,UAAA,kBAAA8G,GAAA,CAAAlH,OAAA,CAAAI,UAAA,CAAA2B,oBAAA,EAAqJ;QAqCrJrD,EAAA,CAAAI,SAAA,EAAkK;QAAlKJ,EAAA,CAAAe,UAAA,UAAAyH,GAAA,CAAAlH,OAAA,kBAAAkH,GAAA,CAAAlH,OAAA,CAAAI,UAAA,kBAAA8G,GAAA,CAAAlH,OAAA,CAAAI,UAAA,CAAAwC,SAAA,MAAAsE,GAAA,CAAAlH,OAAA,kBAAAkH,GAAA,CAAAlH,OAAA,CAAAI,UAAA,kBAAA8G,GAAA,CAAAlH,OAAA,CAAAI,UAAA,CAAAiD,aAAA,kBAAA6D,GAAA,CAAAlH,OAAA,CAAAI,UAAA,CAAAiD,aAAA,CAAAY,MAAA,UAAAiD,GAAA,CAAAlH,OAAA,kBAAAkH,GAAA,CAAAlH,OAAA,CAAAI,UAAA,kBAAA8G,GAAA,CAAAlH,OAAA,CAAAI,UAAA,CAAAoD,qBAAA,MAAA0D,GAAA,CAAAlH,OAAA,kBAAAkH,GAAA,CAAAlH,OAAA,CAAAI,UAAA,kBAAA8G,GAAA,CAAAlH,OAAA,CAAAI,UAAA,CAAAwD,KAAA,EAAkK;QA8F/GlF,EAAA,CAAAI,SAAA,IAAwB;QAAxBJ,EAAA,CAAAwB,iBAAA,CAAAgH,GAAA,CAAAlH,OAAA,kBAAAkH,GAAA,CAAAlH,OAAA,CAAA+H,SAAA,CAAwB;QAW7BrJ,EAAA,CAAAI,SAAA,GAAgB;QAAhBJ,EAAA,CAAAwB,iBAAA,CAAAgH,GAAA,CAAA/C,UAAA,CAAgB;QAKvDzF,EAAA,CAAAI,SAAA,GAA2B;QAA3BJ,EAAA,CAAAe,UAAA,SAAAyH,GAAA,CAAAvC,SAAA,aAA2B;QAGzBjG,EAAA,CAAAI,SAAA,EAA2B;QAA3BJ,EAAA,CAAAe,UAAA,SAAAyH,GAAA,CAAAvC,SAAA,aAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}