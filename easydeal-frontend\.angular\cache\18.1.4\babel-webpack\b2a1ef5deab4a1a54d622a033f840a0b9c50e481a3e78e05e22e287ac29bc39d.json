{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../developer/services/projects.service\";\nimport * as i3 from \"../../services/authentication.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/router\";\nconst _c0 = () => [\"/broker/dashboard\"];\nfunction BrokerRegistrationStepperComponent_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function BrokerRegistrationStepperComponent_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.previousStep());\n    });\n    i0.ɵɵtext(1, \" Back to previous step \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_12_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"fullName\"), \" \");\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_12_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"input\"), \" \");\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_12_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 30);\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"h3\", 15);\n    i0.ɵɵtext(2, \"Enter Your Basic Information\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 16)(4, \"label\", 17);\n    i0.ɵɵelement(5, \"i\", 18);\n    i0.ɵɵtext(6, \" Full Name \");\n    i0.ɵɵelement(7, \"span\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"input\", 20);\n    i0.ɵɵlistener(\"blur\", function BrokerRegistrationStepperComponent_div_12_Template_input_blur_8_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.markFieldAsTouched(\"fullName\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, BrokerRegistrationStepperComponent_div_12_div_9_Template, 2, 1, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 16)(11, \"label\", 22);\n    i0.ɵɵelement(12, \"i\", 23);\n    i0.ɵɵtext(13, \" Enter Email or Phone Number \");\n    i0.ɵɵelement(14, \"span\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"input\", 24);\n    i0.ɵɵlistener(\"blur\", function BrokerRegistrationStepperComponent_div_12_Template_input_blur_15_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.markFieldAsTouched(\"input\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, BrokerRegistrationStepperComponent_div_12_div_16_Template, 2, 1, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function BrokerRegistrationStepperComponent_div_12_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.handleNextStepAndSendCode());\n    });\n    i0.ɵɵtemplate(18, BrokerRegistrationStepperComponent_div_12_span_18_Template, 1, 0, \"span\", 26);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 27);\n    i0.ɵɵtext(21, \" Need help? \");\n    i0.ɵɵelementStart(22, \"span\", 28);\n    i0.ɵɵtext(23, \"Contact us\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.isFieldInvalid(\"fullName\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(\"fullName\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.isFieldInvalid(\"input\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(\"input\"));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"loading\", ctx_r1.isLoadingSendOtp);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.isStep1Valid() || ctx_r1.isLoadingSendOtp);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoadingSendOtp);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isLoadingSendOtp ? \"Sending...\" : \"Send Verification Code\", \" \");\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_13_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"input\", 39);\n    i0.ɵɵlistener(\"input\", function BrokerRegistrationStepperComponent_div_13_div_5_Template_input_input_1_listener($event) {\n      const i_r6 = i0.ɵɵrestoreView(_r5).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.autoFocusNext($event, i_r6));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctrl_r7 = ctx.$implicit;\n    const i_r6 = ctx.index;\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"is-invalid\", ctrl_r7.invalid && ctrl_r7.touched);\n    i0.ɵɵproperty(\"formControlName\", i_r6);\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_13_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 40);\n    i0.ɵɵtext(1, \" Resend in \");\n    i0.ɵɵelementStart(2, \"span\", 41);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" 0:\", ctx_r1.countdown < 10 ? \"0\" + ctx_r1.countdown : ctx_r1.countdown, \" \");\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_13_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function BrokerRegistrationStepperComponent_div_13_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onResendCode());\n    });\n    i0.ɵɵtext(1, \" Resend Code \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_13_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.otpErrorMessage, \" \");\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_13_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 30);\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"h3\", 15);\n    i0.ɵɵtext(2, \"Enter Verification Code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 31)(4, \"div\", 32);\n    i0.ɵɵtemplate(5, BrokerRegistrationStepperComponent_div_13_div_5_Template, 2, 3, \"div\", 33);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 34);\n    i0.ɵɵtemplate(7, BrokerRegistrationStepperComponent_div_13_span_7_Template, 4, 1, \"span\", 35)(8, BrokerRegistrationStepperComponent_div_13_button_8_Template, 2, 0, \"button\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, BrokerRegistrationStepperComponent_div_13_div_9_Template, 2, 1, \"div\", 37);\n    i0.ɵɵelementStart(10, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function BrokerRegistrationStepperComponent_div_13_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.checkOTP());\n    });\n    i0.ɵɵtemplate(11, BrokerRegistrationStepperComponent_div_13_span_11_Template, 1, 0, \"span\", 26);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 27);\n    i0.ɵɵtext(14, \" Need help? \");\n    i0.ɵɵelementStart(15, \"span\", 28);\n    i0.ɵɵtext(16, \"Contact us\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.verificationCodeControls);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.showResendButton);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showResendButton);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.otpErrorMessage);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"loading\", ctx_r1.isLoadingCheckOtp);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.isStep2Valid() || ctx_r1.isLoadingCheckOtp);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoadingCheckOtp);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isLoadingCheckOtp ? \"Verifying...\" : \"Verified - Next\", \" \");\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"h3\", 15);\n    i0.ɵɵtext(2, \"Choose Broker Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 44)(4, \"p\", 45);\n    i0.ɵɵtext(5, \" Please select the type of broker registration you want to proceed with \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 46)(7, \"div\", 47);\n    i0.ɵɵlistener(\"click\", function BrokerRegistrationStepperComponent_div_14_Template_div_click_7_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.selectBrokerType(\"independent\"));\n    });\n    i0.ɵɵelementStart(8, \"div\", 48);\n    i0.ɵɵelement(9, \"i\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"h4\", 49);\n    i0.ɵɵtext(11, \"Independent Broker\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 50);\n    i0.ɵɵtext(13, \" Register as an individual real estate broker \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 47);\n    i0.ɵɵlistener(\"click\", function BrokerRegistrationStepperComponent_div_14_Template_div_click_14_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.selectBrokerType(\"real_estate_brokage_company\"));\n    });\n    i0.ɵɵelementStart(15, \"div\", 48);\n    i0.ɵɵelement(16, \"i\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"h4\", 49);\n    i0.ɵɵtext(18, \"Real Estate Company\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"p\", 50);\n    i0.ɵɵtext(20, \" Register as a real estate brokerage company \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function BrokerRegistrationStepperComponent_div_14_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextStep());\n    });\n    i0.ɵɵtext(22, \" Continue \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 27);\n    i0.ɵɵtext(24, \" Need help? \");\n    i0.ɵɵelementStart(25, \"span\", 28);\n    i0.ɵɵtext(26, \"Contact us\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"selected\", ctx_r1.brokerType === \"independent\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"selected\", ctx_r1.brokerType === \"real_estate_brokage_company\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.brokerType);\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_15_div_7_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 71);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFileCount(\"image\"), \" \");\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_15_div_7_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 71);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFileCount(\"idFront\"), \" \");\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_15_div_7_span_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 71);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFileCount(\"idBack\"), \" \");\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_15_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 59)(2, \"label\", 60)(3, \"div\", 61);\n    i0.ɵɵelement(4, \"i\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 63);\n    i0.ɵɵtext(6, \" Profile Photo \");\n    i0.ɵɵtemplate(7, BrokerRegistrationStepperComponent_div_15_div_7_span_7_Template, 2, 1, \"span\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 65);\n    i0.ɵɵtext(9, \"PNG, JPG\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"input\", 66);\n    i0.ɵɵlistener(\"change\", function BrokerRegistrationStepperComponent_div_15_div_7_Template_input_change_10_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onFileChange($event, \"image\"));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 59)(12, \"label\", 67)(13, \"div\", 61);\n    i0.ɵɵelement(14, \"i\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 63);\n    i0.ɵɵtext(16, \" National ID Front \");\n    i0.ɵɵtemplate(17, BrokerRegistrationStepperComponent_div_15_div_7_span_17_Template, 2, 1, \"span\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 65);\n    i0.ɵɵtext(19, \"PNG, JPG, PDF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"input\", 68);\n    i0.ɵɵlistener(\"change\", function BrokerRegistrationStepperComponent_div_15_div_7_Template_input_change_20_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onFileChange($event, \"idFront\"));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"div\", 59)(22, \"label\", 69)(23, \"div\", 61);\n    i0.ɵɵelement(24, \"i\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"span\", 63);\n    i0.ɵɵtext(26, \" National ID Back \");\n    i0.ɵɵtemplate(27, BrokerRegistrationStepperComponent_div_15_div_7_span_27_Template, 2, 1, \"span\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 65);\n    i0.ɵɵtext(29, \"PNG, JPG, PDF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"input\", 70);\n    i0.ɵɵlistener(\"change\", function BrokerRegistrationStepperComponent_div_15_div_7_Template_input_change_30_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onFileChange($event, \"idBack\"));\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFileCount(\"image\") > 0);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFileCount(\"idFront\") > 0);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFileCount(\"idBack\") > 0);\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_15_div_8_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 71);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFileCount(\"image\"), \" \");\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_15_div_8_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 71);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFileCount(\"commercialRegistryImage\"), \" \");\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_15_div_8_span_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 71);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFileCount(\"taxCardImage\"), \" \");\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_15_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 59)(2, \"label\", 60)(3, \"div\", 61);\n    i0.ɵɵelement(4, \"i\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 63);\n    i0.ɵɵtext(6, \" Company logo image for account \");\n    i0.ɵɵtemplate(7, BrokerRegistrationStepperComponent_div_15_div_8_span_7_Template, 2, 1, \"span\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 65);\n    i0.ɵɵtext(9, \"PNG, JPG\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"input\", 66);\n    i0.ɵɵlistener(\"change\", function BrokerRegistrationStepperComponent_div_15_div_8_Template_input_change_10_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onFileChange($event, \"image\"));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 59)(12, \"label\", 72)(13, \"div\", 61);\n    i0.ɵɵelement(14, \"i\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 63);\n    i0.ɵɵtext(16, \" Commercial register photo \");\n    i0.ɵɵtemplate(17, BrokerRegistrationStepperComponent_div_15_div_8_span_17_Template, 2, 1, \"span\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 65);\n    i0.ɵɵtext(19, \"PNG, JPG, PDF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"input\", 73);\n    i0.ɵɵlistener(\"change\", function BrokerRegistrationStepperComponent_div_15_div_8_Template_input_change_20_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onFileChange($event, \"commercialRegistryImage\"));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"div\", 59)(22, \"label\", 74)(23, \"div\", 61);\n    i0.ɵɵelement(24, \"i\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"span\", 63);\n    i0.ɵɵtext(26, \" Tax card image \");\n    i0.ɵɵtemplate(27, BrokerRegistrationStepperComponent_div_15_div_8_span_27_Template, 2, 1, \"span\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 65);\n    i0.ɵɵtext(29, \"PNG, JPG, PDF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"input\", 75);\n    i0.ɵɵlistener(\"change\", function BrokerRegistrationStepperComponent_div_15_div_8_Template_input_change_30_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onFileChange($event, \"taxCardImage\"));\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFileCount(\"image\") > 0);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFileCount(\"commercialRegistryImage\") > 0);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFileCount(\"taxCardImage\") > 0);\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"h3\", 15);\n    i0.ɵɵtext(2, \"Please Upload Required Documents\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 52)(4, \"p\", 53);\n    i0.ɵɵtext(5, \" You can upload the required documents now or skip and add them later when you first use the required services \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 54);\n    i0.ɵɵtemplate(7, BrokerRegistrationStepperComponent_div_15_div_7_Template, 31, 3, \"div\", 55)(8, BrokerRegistrationStepperComponent_div_15_div_8_Template, 31, 3, \"div\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function BrokerRegistrationStepperComponent_div_15_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextStep());\n    });\n    i0.ɵɵtext(10, \" Upload Documents \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function BrokerRegistrationStepperComponent_div_15_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextStep());\n    });\n    i0.ɵɵtext(12, \" Skip and return later \");\n    i0.ɵɵelement(13, \"i\", 58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 27);\n    i0.ɵɵtext(15, \" Need help? \");\n    i0.ɵɵelementStart(16, \"span\", 28);\n    i0.ɵɵtext(17, \"Contact us\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.brokerType === \"independent\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.brokerType === \"real_estate_brokage_company\");\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_16_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 83);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const city_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", city_r14.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", city_r14.name_en, \" \");\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_16_option_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 83);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const area_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", area_r15.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", area_r15.name_en, \" \");\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_16_option_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 83);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const subarea_r16 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", subarea_r16.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(subarea_r16.name_en);\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"h3\", 15);\n    i0.ɵɵtext(2, \"Choose Your Work Areas\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 77)(4, \"div\", 16)(5, \"label\", 78);\n    i0.ɵɵtext(6, \"City\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"select\", 79);\n    i0.ɵɵlistener(\"change\", function BrokerRegistrationStepperComponent_div_16_Template_select_change_7_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onCityChange($event));\n    });\n    i0.ɵɵelementStart(8, \"option\", 80);\n    i0.ɵɵtext(9, \"Select City\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, BrokerRegistrationStepperComponent_div_16_option_10_Template, 2, 2, \"option\", 81);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 16)(12, \"label\", 78);\n    i0.ɵɵtext(13, \"Area\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"select\", 82);\n    i0.ɵɵlistener(\"change\", function BrokerRegistrationStepperComponent_div_16_Template_select_change_14_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAreaChange($event));\n    });\n    i0.ɵɵelementStart(15, \"option\", 80);\n    i0.ɵɵtext(16, \"Select Area\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, BrokerRegistrationStepperComponent_div_16_option_17_Template, 2, 2, \"option\", 81);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 16)(19, \"label\", 78);\n    i0.ɵɵtext(20, \"sub Area\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"select\", 82);\n    i0.ɵɵlistener(\"change\", function BrokerRegistrationStepperComponent_div_16_Template_select_change_21_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSubAreaChange($event));\n    });\n    i0.ɵɵelementStart(22, \"option\", 80);\n    i0.ɵɵtext(23, \"Select Sub Area\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, BrokerRegistrationStepperComponent_div_16_option_24_Template, 2, 2, \"option\", 81);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function BrokerRegistrationStepperComponent_div_16_Template_button_click_25_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextStep());\n    });\n    i0.ɵɵtext(26, \" Continue to Specializations \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function BrokerRegistrationStepperComponent_div_16_Template_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextStepSkipping());\n    });\n    i0.ɵɵtext(28, \" Skip and return later \");\n    i0.ɵɵelement(29, \"i\", 58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 27);\n    i0.ɵɵtext(31, \" Need help? \");\n    i0.ɵɵelementStart(32, \"span\", 28);\n    i0.ɵɵtext(33, \"Contact us\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"value\", ctx_r1.selectedCityId);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.cities);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"value\", ctx_r1.selectedAreaId)(\"disabled\", !ctx_r1.selectedCityId);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.areas);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"value\", ctx_r1.selectedSubAreaId)(\"disabled\", !ctx_r1.selectedAreaId);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.subAreas);\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_17_div_6_div_7_label_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 96)(1, \"input\", 97);\n    i0.ɵɵlistener(\"change\", function BrokerRegistrationStepperComponent_div_17_div_6_div_7_label_2_Template_input_change_1_listener($event) {\n      const type_r21 = i0.ɵɵrestoreView(_r20).$implicit;\n      const scope_r19 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSpecializationChange(scope_r19, type_r21, $event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\", 98);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const type_r21 = ctx.$implicit;\n    const scope_r19 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", scope_r19.selectedTypes == null ? null : scope_r19.selectedTypes.includes(type_r21));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.specializationDisplayMap[type_r21] || type_r21);\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_17_div_6_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 93)(1, \"div\", 94);\n    i0.ɵɵtemplate(2, BrokerRegistrationStepperComponent_div_17_div_6_div_7_label_2_Template, 4, 2, \"label\", 95);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const scope_r19 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", scope_r19.specializations);\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_17_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"div\", 87)(2, \"label\", 88)(3, \"input\", 89);\n    i0.ɵɵlistener(\"change\", function BrokerRegistrationStepperComponent_div_17_div_6_Template_input_change_3_listener($event) {\n      const scope_r19 = i0.ɵɵrestoreView(_r18).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onScopeSelectionChange(scope_r19, $event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 90);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"i\", 91);\n    i0.ɵɵlistener(\"click\", function BrokerRegistrationStepperComponent_div_17_div_6_Template_i_click_6_listener() {\n      const scope_r19 = i0.ɵɵrestoreView(_r18).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleSpecializationScope(scope_r19));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, BrokerRegistrationStepperComponent_div_17_div_6_div_7_Template, 3, 1, \"div\", 92);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const scope_r19 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.isOtherScopeExpanded(scope_r19));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", scope_r19.selected)(\"disabled\", ctx_r1.isOtherScopeExpanded(scope_r19));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(scope_r19.specialization_scope);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(scope_r19.expanded ? \"ki-outline ki-up\" : \"ki-outline ki-down\");\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.isOtherScopeExpanded(scope_r19));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", scope_r19.expanded);\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"h3\", 15);\n    i0.ɵɵtext(2, \"Choose Your Specializations\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 77)(4, \"div\", 16)(5, \"div\", 84);\n    i0.ɵɵtemplate(6, BrokerRegistrationStepperComponent_div_17_div_6_Template, 8, 10, \"div\", 85);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function BrokerRegistrationStepperComponent_div_17_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextStep());\n    });\n    i0.ɵɵtext(8, \" Continue to Account Setup \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function BrokerRegistrationStepperComponent_div_17_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextStep());\n    });\n    i0.ɵɵtext(10, \" Skip and return later \");\n    i0.ɵɵelement(11, \"i\", 58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 27);\n    i0.ɵɵtext(13, \" Need help? \");\n    i0.ɵɵelementStart(14, \"span\", 28);\n    i0.ɵɵtext(15, \"Contact us\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.staticScopes);\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_18_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"phone\"), \" \");\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_18_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"email\"), \" \");\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_18_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"password\"), \" \");\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_18_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"password_confirmation\"), \" \");\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_18_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFormError(), \" \");\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_18_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"agreeTerms\"), \" \");\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_18_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.createAccountErrorMessage, \" \");\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_18_span_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 30);\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"h3\", 15);\n    i0.ɵɵtext(2, \"Enter Your Account Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 16)(4, \"label\", 99);\n    i0.ɵɵelement(5, \"i\", 23);\n    i0.ɵɵtext(6, \" Phone \");\n    i0.ɵɵelement(7, \"span\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"input\", 100);\n    i0.ɵɵlistener(\"blur\", function BrokerRegistrationStepperComponent_div_18_Template_input_blur_8_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.markFieldAsTouched(\"phone\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, BrokerRegistrationStepperComponent_div_18_div_9_Template, 2, 1, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 16)(11, \"label\", 101);\n    i0.ɵɵelement(12, \"i\", 18);\n    i0.ɵɵtext(13, \" Email \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"input\", 102);\n    i0.ɵɵlistener(\"blur\", function BrokerRegistrationStepperComponent_div_18_Template_input_blur_14_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.markFieldAsTouched(\"email\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, BrokerRegistrationStepperComponent_div_18_div_15_Template, 2, 1, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 16)(17, \"label\", 103);\n    i0.ɵɵelement(18, \"i\", 104);\n    i0.ɵɵtext(19, \" Password \");\n    i0.ɵɵelement(20, \"span\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"input\", 105);\n    i0.ɵɵlistener(\"blur\", function BrokerRegistrationStepperComponent_div_18_Template_input_blur_21_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.markFieldAsTouched(\"password\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, BrokerRegistrationStepperComponent_div_18_div_22_Template, 2, 1, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 16)(24, \"label\", 106);\n    i0.ɵɵelement(25, \"i\", 104);\n    i0.ɵɵtext(26, \" Confirm Password \");\n    i0.ɵɵelement(27, \"span\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"input\", 107);\n    i0.ɵɵlistener(\"blur\", function BrokerRegistrationStepperComponent_div_18_Template_input_blur_28_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.markFieldAsTouched(\"password_confirmation\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(29, BrokerRegistrationStepperComponent_div_18_div_29_Template, 2, 1, \"div\", 21)(30, BrokerRegistrationStepperComponent_div_18_div_30_Template, 2, 1, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 16)(32, \"div\", 108);\n    i0.ɵɵelement(33, \"input\", 109);\n    i0.ɵɵelementStart(34, \"label\", 110);\n    i0.ɵɵtext(35, \" I agree to the Terms and Conditions \");\n    i0.ɵɵelement(36, \"span\", 19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(37, BrokerRegistrationStepperComponent_div_18_div_37_Template, 2, 1, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(38, BrokerRegistrationStepperComponent_div_18_div_38_Template, 2, 1, \"div\", 37);\n    i0.ɵɵelementStart(39, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function BrokerRegistrationStepperComponent_div_18_Template_button_click_39_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.createAccount());\n    });\n    i0.ɵɵtemplate(40, BrokerRegistrationStepperComponent_div_18_span_40_Template, 1, 0, \"span\", 26);\n    i0.ɵɵtext(41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"div\", 27);\n    i0.ɵɵtext(43, \" Need help? \");\n    i0.ɵɵelementStart(44, \"span\", 28);\n    i0.ɵɵtext(45, \"Contact us\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.isFieldInvalid(\"phone\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(\"phone\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.isFieldInvalid(\"email\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(\"email\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.isFieldInvalid(\"password\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(\"password\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.isFieldInvalid(\"password_confirmation\") || ctx_r1.getFormError());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(\"password_confirmation\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFormError());\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.isFieldInvalid(\"agreeTerms\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(\"agreeTerms\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.createAccountErrorMessage);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"loading\", ctx_r1.isLoadingCreateAccount);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.isStep7Valid() || ctx_r1.isLoadingCreateAccount);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoadingCreateAccount);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isLoadingCreateAccount ? \"Creating Account...\" : \"Create Account\", \" \");\n  }\n}\nfunction BrokerRegistrationStepperComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 111)(1, \"div\", 112)(2, \"div\", 113);\n    i0.ɵɵelement(3, \"i\", 114);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\", 115);\n    i0.ɵɵtext(5, \"Registration Successful\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 116);\n    i0.ɵɵtext(7, \" Your account has been successfully created. You can now enjoy the various and amazing services provided by Easy Deal through the website or dashboard. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 117);\n    i0.ɵɵelement(9, \"img\", 118);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 119);\n    i0.ɵɵtext(11, \" Go to Dashboard \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 120)(13, \"span\", 121);\n    i0.ɵɵtext(14, \"Learn all about your account and how to get started\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nconst SPECIALIZATION_MAPPING = {\n  purchase_sell_outside_compound: ['purchasing_sell_residential_outside_compound', 'purchasing_sell_national_housing_projects_outside_compound', 'purchasing_sell_administrative_commercial_units_outside_compound', 'purchasing_sell_industrial_and_warehousing_outside_compound', 'purchasing_sell_lands_and_ready_projects_outside_compound', 'purchasing_sell_villas_and_buildings_outside_compound'],\n  // purchase_sell_inside_compound: [\n  //   'purchasing_sell_residential_inside_compound',\n  //   'purchasing_sell_villas_inside_compound',\n  //   'purchasing_sell_administrative_commercial_Units_inside_compound',\n  // ],\n  primary_inside_compound: ['purchasing_sell_residential_inside_compound', 'purchasing_sell_villas_inside_compound', 'purchasing_sell_administrative_commercial_units_inside_compound'],\n  resale_inside_compound: ['purchasing_sell_residential_inside_compound', 'purchasing_sell_villas_inside_compound', 'purchasing_sell_administrative_commercial_units_inside_compound'],\n  rentals_outside_compound: ['rent_residential_outside_compound', 'rent_national_housing_projects_outside_compound', 'rent_administrative_commercial_units_outside_compound', 'rent_industrial_and_warehousing_outside_compound', 'rent_hotel_Units_outside_compound'],\n  rentals_inside_compound: ['rent_residential_inside_compound', 'rent_hotel_Units_inside_compound', 'rent_administrative_commercial_units_inside_compound']\n};\nexport class BrokerRegistrationStepperComponent {\n  fb;\n  projectsService;\n  cd;\n  authenticationService;\n  onBack = new EventEmitter();\n  onComplete = new EventEmitter();\n  registrationForm;\n  currentStep = 1;\n  totalSteps = 8;\n  uploadedFiles = {};\n  brokerType = '';\n  verificationDigits = ['', '', '', '', ''];\n  countdown = 25;\n  showResendButton = false;\n  isLoadingSendOtp = false;\n  isLoadingCheckOtp = false;\n  isLoadingCreateAccount = false;\n  otpErrorMessage = '';\n  createAccountErrorMessage = '';\n  // Validators\n  static noNumbers = Validators.pattern(/^[^0-9]*$/);\n  static emailOrPhonePattern = Validators.pattern(/^([^\\s@]+@[^\\s@]+\\.[^\\s@]+|01[0125]\\d{8}|05\\d{8}|\\+201[0125]\\d{8}|\\+9665\\d{8})$/);\n  static phonePattern = Validators.pattern(/^(01[0125]\\d{8}|05\\d{8}|\\+201[0125]\\d{8}|\\+9665\\d{8})$/);\n  static passwordPattern = Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/);\n  // Location data\n  cities = [];\n  areas = [];\n  subAreas = [];\n  selectedCityId = 0;\n  selectedCityName = '';\n  selectedAreaId = 0;\n  selectedAreaName = '';\n  selectedSubAreaId = 0;\n  selectedSubAreaName = '';\n  step2Form;\n  // Specializations\n  staticScopes = [];\n  constructor(fb, projectsService, cd, authenticationService) {\n    this.fb = fb;\n    this.projectsService = projectsService;\n    this.cd = cd;\n    this.authenticationService = authenticationService;\n    this.registrationForm = this.createForm();\n    this.step2Form = this.fb.group({\n      cityId: [0],\n      areaId: [0],\n      subAreaId: [0]\n    });\n    this.initializeSpecializations();\n    this.loadCities();\n  }\n  createForm() {\n    return this.fb.group({\n      // Step 1: Basic Information\n      fullName: ['', [Validators.required, Validators.minLength(2), BrokerRegistrationStepperComponent.noNumbers]],\n      input: ['', [Validators.required, BrokerRegistrationStepperComponent.emailOrPhonePattern]],\n      // Step 2: Verification Code\n      verificationCode: this.fb.array(Array(5).fill('').map(() => this.fb.control('', [Validators.required, Validators.pattern('[0-9]')]))),\n      // Step 7: Phone, Email, Password, Terms\n      phone: ['', [Validators.required, BrokerRegistrationStepperComponent.phonePattern]],\n      email: ['', [Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(8), BrokerRegistrationStepperComponent.passwordPattern]],\n      password_confirmation: ['', [Validators.required]],\n      agreeTerms: [false, [Validators.requiredTrue]]\n    });\n  }\n  initializeSpecializations() {\n    this.staticScopes = Object.entries(SPECIALIZATION_MAPPING).map(([scope, specializations]) => ({\n      specialization_scope: scope,\n      specializations,\n      expanded: false,\n      selected: false,\n      selectedTypes: []\n    }));\n  }\n  nextStep() {\n    if (this.currentStep < this.totalSteps) {\n      // Clear OTP error message when leaving step 2\n      if (this.currentStep === 2) {\n        this.otpErrorMessage = '';\n      }\n      this.currentStep++;\n    }\n  }\n  nextStepSkipping() {\n    if (this.currentStep < this.totalSteps) {\n      // Clear OTP error message when leaving step 2\n      if (this.currentStep === 2) {\n        this.otpErrorMessage = '';\n      }\n      if (this.currentStep == 5) {\n        this.currentStep = 6;\n      }\n      this.currentStep++;\n    }\n  }\n  previousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    } else {\n      this.onBack.emit();\n    }\n  }\n  onFileChange(event, fileType) {\n    const files = event.target.files;\n    if (!files?.length) return;\n    // Clear previous files and add new ones (replace instead of append)\n    this.uploadedFiles[fileType] = Array.from(files);\n    console.log(`Uploaded ${files.length} file(s) for ${fileType} (replaced previous files)`);\n  }\n  getFileCount(fileType) {\n    return this.uploadedFiles[fileType]?.length || 0;\n  }\n  // Clear files for a specific file type\n  clearFiles(fileType) {\n    delete this.uploadedFiles[fileType];\n    console.log(`Cleared all files for ${fileType}`);\n  }\n  // Clear all uploaded files\n  clearAllFiles() {\n    this.uploadedFiles = {};\n    console.log('Cleared all uploaded files');\n  }\n  // Location Methods\n  loadCities() {\n    this.projectsService.getCities().subscribe({\n      next: response => {\n        if (response && response.data) {\n          this.cities = response.data;\n        } else {\n          console.warn('No cities data in response');\n          this.cities = [];\n        }\n      },\n      error: err => {\n        console.error('Error loading cities:', err);\n      },\n      complete: () => {\n        this.cd.detectChanges();\n      }\n    });\n  }\n  loadAreas(cityId) {\n    this.projectsService.getAreas(cityId).subscribe({\n      next: response => {\n        if (response && response.data) {\n          this.areas = response.data;\n        } else {\n          console.warn('No areas data in response');\n          this.areas = [];\n        }\n      },\n      error: err => {\n        console.error('Error loading areas:', err);\n        this.areas = [];\n      },\n      complete: () => {\n        this.cd.detectChanges();\n      }\n    });\n  }\n  loadSubAreas(areaId) {\n    this.projectsService.getSubAreas(areaId).subscribe({\n      next: response => {\n        if (response && response.data) {\n          this.subAreas = response.data;\n        } else {\n          console.warn('No areas data in response');\n          this.subAreas = [];\n        }\n      },\n      error: err => {\n        console.error('Error loading areas:', err);\n        this.subAreas = [];\n      },\n      complete: () => {\n        this.cd.detectChanges();\n      }\n    });\n  }\n  onCityChange(event) {\n    const cityId = event.target.value;\n    this.selectedCityId = cityId;\n    this.selectedCityName = event.target.options[event.target.selectedIndex].text;\n    this.step2Form.patchValue({\n      cityId: cityId\n    });\n    // Reset area\n    this.selectedAreaId = 0;\n    this.selectedAreaName = '';\n    this.step2Form.patchValue({\n      areaId: 0\n    });\n    this.areas = [];\n    this.loadAreas(cityId);\n  }\n  onAreaChange(event) {\n    const areaId = event.target.value;\n    this.selectedAreaId = areaId;\n    this.selectedAreaName = event.target.options[event.target.selectedIndex].text;\n    this.step2Form.patchValue({\n      areaId: areaId\n    });\n    this.loadSubAreas(areaId);\n  }\n  onSubAreaChange(event) {\n    const subAreaId = event.target.value;\n    this.selectedSubAreaId = subAreaId;\n    this.selectedSubAreaName = event.target.options[event.target.selectedIndex].text;\n    this.step2Form.patchValue({\n      subAreaId: subAreaId\n    });\n  }\n  // Specialization Methods\n  toggleSpecializationScope(scope) {\n    this.staticScopes.forEach(s => {\n      if (s !== scope) s.expanded = false;\n    });\n    scope.expanded = !scope.expanded;\n  }\n  onScopeSelectionChange(scope, event) {\n    scope.selected = event.target.checked;\n    console.log(scope);\n  }\n  onSpecializationChange(scope, type, event) {\n    if (!scope.selectedTypes) {\n      scope.selectedTypes = [];\n    }\n    if (event.target.checked) {\n      if (!scope.selectedTypes.includes(type)) {\n        scope.selectedTypes.push(type);\n      }\n    } else {\n      scope.selectedTypes = scope.selectedTypes.filter(t => t !== type);\n    }\n    console.log(this.getSpecializationScopesPayload());\n  }\n  // Utility Methods\n  isOtherScopeExpanded(currentScope) {\n    return this.staticScopes.some(scope => scope !== currentScope && scope.expanded);\n  }\n  // Simple action methods\n  selectBrokerType(type) {\n    this.brokerType = type;\n    console.log(this.brokerType);\n  }\n  get verificationCodeControls() {\n    return this.registrationForm.get('verificationCode').controls;\n  }\n  onDigitInput(index) {\n    const code = this.verificationDigits.join('');\n    this.registrationForm.patchValue({\n      verificationCode: code\n    });\n  }\n  // Helper methods for validation\n  isFieldInvalid(fieldName) {\n    const field = this.registrationForm.get(fieldName);\n    return !!(field && field.invalid && (field.dirty || field.touched));\n  }\n  markFieldAsTouched(fieldName) {\n    this.registrationForm.get(fieldName)?.markAsTouched();\n  }\n  getFieldError(fieldName) {\n    const field = this.registrationForm.get(fieldName);\n    if (!field?.errors) return '';\n    const errors = field.errors;\n    if (errors['required']) return 'This field is required';\n    if (errors['pattern'] && fieldName === 'fullName') return 'Name cannot contain numbers';\n    if (errors['pattern'] && fieldName === 'input') return 'Enter valid email or phone number';\n    if (errors['pattern'] && fieldName === 'phone') return 'Enter valid phone number';\n    if (errors['pattern'] && fieldName === 'password') return 'Need uppercase, lowercase & number';\n    if (errors['email']) return 'Enter valid email';\n    if (errors['minlength']) return `Min ${errors['minlength'].requiredLength} chars`;\n    return 'Invalid input';\n  }\n  getFormError() {\n    const password = this.registrationForm.get('password')?.value;\n    const confirmPassword = this.registrationForm.get('password_confirmation')?.value;\n    if (password && confirmPassword && password !== confirmPassword) {\n      return 'Passwords do not match';\n    }\n    return '';\n  }\n  // Check if step is valid\n  isStep1Valid() {\n    const fullName = this.registrationForm.get('fullName');\n    const input = this.registrationForm.get('input');\n    return !!(fullName?.valid && input?.valid);\n  }\n  isStep2Valid() {\n    const verificationCode = this.registrationForm.get('verificationCode');\n    return verificationCode.valid;\n  }\n  isStep7Valid() {\n    const phone = this.registrationForm.get('phone');\n    const email = this.registrationForm.get('email');\n    const password = this.registrationForm.get('password');\n    const passwordConfirmation = this.registrationForm.get('password_confirmation');\n    const agreeTerms = this.registrationForm.get('agreeTerms');\n    // Check if passwords match\n    const passwordsMatch = password?.value === passwordConfirmation?.value;\n    return !!(phone?.valid && email?.valid && password?.valid && passwordConfirmation?.valid && agreeTerms?.valid && passwordsMatch);\n  }\n  handleNextStepAndSendCode() {\n    this.sendVerificationCode(true);\n  }\n  sendVerificationCode(moveToNextStep = false) {\n    if (!this.isStep1Valid()) {\n      this.markFieldAsTouched('fullName');\n      this.markFieldAsTouched('input');\n      return;\n    }\n    this.isLoadingSendOtp = true;\n    this.otpErrorMessage = '';\n    const input = this.registrationForm.get('input')?.value?.trim();\n    let params = {};\n    const isEmail = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(input);\n    const isPhone = /^[0-9+\\-\\s]{7,15}$/.test(input);\n    if (isEmail) {\n      params.email = input;\n    } else if (isPhone) {\n      params.phone = input;\n    }\n    this.authenticationService.sendOtp(params).subscribe({\n      next: response => {\n        console.log('OTP sent:', response);\n        this.isLoadingSendOtp = false;\n        this.startCountdown();\n        if (moveToNextStep) {\n          this.nextStep();\n        }\n        this.cd.markForCheck();\n      },\n      error: error => {\n        console.error('Failed to send OTP:', error);\n        this.isLoadingSendOtp = false;\n        this.otpErrorMessage = error.message || 'Failed to send verification code. Please try again.';\n        this.cd.markForCheck();\n      }\n    });\n  }\n  checkOTP() {\n    if (!this.isStep2Valid()) {\n      // Mark all verification code inputs as touched\n      const verificationCodeArray = this.registrationForm.get('verificationCode');\n      verificationCodeArray.controls.forEach(control => control.markAsTouched());\n      return;\n    }\n    this.isLoadingCheckOtp = true;\n    this.otpErrorMessage = '';\n    const input = this.registrationForm.get('input')?.value?.trim();\n    const codeArray = this.registrationForm.get('verificationCode')?.value;\n    const otp = codeArray.join('');\n    let params = {};\n    const isEmail = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(input);\n    const isPhone = /^[0-9+\\-\\s]{7,15}$/.test(input);\n    if (isEmail) {\n      params.email = input;\n    } else if (isPhone) {\n      params.phone = input;\n    }\n    params.otp = otp;\n    this.authenticationService.checkOtp(params).subscribe({\n      next: response => {\n        console.log('OTP checked:', response);\n        this.isLoadingCheckOtp = false;\n        this.nextStep();\n        this.cd.markForCheck();\n      },\n      error: error => {\n        console.error('Failed to check OTP:', error);\n        this.isLoadingCheckOtp = false;\n        this.otpErrorMessage = error?.error?.message || 'Invalid verification code. Please try again.';\n        this.cd.markForCheck();\n      }\n    });\n  }\n  createAccount() {\n    if (!this.isStep7Valid()) {\n      this.markFieldAsTouched('phone');\n      this.markFieldAsTouched('email');\n      this.markFieldAsTouched('password');\n      this.markFieldAsTouched('password_confirmation');\n      this.markFieldAsTouched('agreeTerms');\n      return;\n    }\n    this.isLoadingCreateAccount = true;\n    this.createAccountErrorMessage = '';\n    const input = this.registrationForm.get('input')?.value?.trim();\n    let params = this.registrationForm.value;\n    params.role = 'broker';\n    params.type = this.brokerType;\n    const isEmail = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(input);\n    const isPhone = /^[0-9+\\-\\s]{7,15}$/.test(input);\n    if (isEmail) {\n      params.email = input;\n    } else if (isPhone) {\n      params.phone = input;\n    }\n    const specializationParams = this.getSpecializationScopesPayload();\n    const formData = new FormData();\n    // Append all form fields\n    for (const key in params) {\n      if (params.hasOwnProperty(key)) {\n        formData.append(key, params[key]);\n      }\n    }\n    if (this.selectedAreaId != 0) {\n      params.areaIds = [this.selectedAreaId];\n      params.areaIds?.forEach(id => formData.append('areaIds[]', id));\n    }\n    if (this.selectedSubAreaId != 0) {\n      params.subAreas = [this.selectedSubAreaId];\n      params.subAreas?.forEach(id => formData.append('subAreas[]', id));\n    }\n    // Append specializationScopes (array-like)\n    for (const key in specializationParams) {\n      const values = specializationParams[key];\n      values.forEach(value => {\n        formData.append(key, value);\n      });\n    }\n    // Append uploaded files (if any)\n    for (const fileType in this.uploadedFiles) {\n      const files = this.uploadedFiles[fileType];\n      if (files?.length) {\n        // Append all files for each field type, replacing any previous files\n        files.forEach(file => {\n          formData.append(fileType, file);\n        });\n      }\n    }\n    this.authenticationService.register(formData).subscribe({\n      next: response => {\n        console.log('registered successfully', response);\n        let user = response.data;\n        localStorage.setItem('authToken', user.authToken);\n        this.authenticationService.setCurrentUser(response.data);\n        this.isLoadingCreateAccount = false;\n        this.nextStep();\n        this.cd.markForCheck();\n      },\n      error: error => {\n        console.error('Failed to register:', error);\n        this.isLoadingCreateAccount = false;\n        this.createAccountErrorMessage = error?.error?.message || 'Failed to create account. Please try again.';\n        this.cd.markForCheck();\n      }\n    });\n  }\n  startCountdown() {\n    this.showResendButton = false;\n    this.countdown = 25;\n    const intervalId = setInterval(() => {\n      this.countdown--;\n      if (this.countdown === 0) {\n        clearInterval(intervalId);\n        this.showResendButton = true;\n      }\n      this.cd.markForCheck();\n    }, 1000);\n  }\n  autoFocusNext(event, index) {\n    const input = event.target;\n    if (input.value && index < 5) {\n      const nextInput = input.parentElement?.nextElementSibling?.querySelector('input');\n      nextInput?.focus();\n    }\n  }\n  clearOtpInputs() {\n    this.verificationDigits = ['', '', '', '', ''];\n    const verificationCodeArray = this.registrationForm.get('verificationCode');\n    verificationCodeArray.controls.forEach(control => {\n      control.setValue('');\n      control.markAsUntouched();\n      control.markAsPristine();\n    });\n  }\n  onResendCode() {\n    this.clearOtpInputs();\n    this.otpErrorMessage = '';\n    this.sendVerificationCode(false);\n  }\n  toSnakeCase(str) {\n    return str.toLowerCase().replace(/[^a-z0-9]+/g, '_').replace(/^_|_$/g, '');\n  }\n  getSpecializationScopesPayload() {\n    const result = {};\n    for (const scope of this.staticScopes) {\n      if (scope.selectedTypes && scope.selectedTypes.length > 0) {\n        const key = this.toSnakeCase(scope.specialization_scope);\n        result[`specializationScopes[${key}][]`] = scope.selectedTypes.map(type => this.toSnakeCase(type));\n      }\n    }\n    return result;\n  }\n  specializationDisplayMap = {\n    'purchasing_sell_residential_outside_compound': 'Apartments - duplexes - penthouses - roof - basements - studio',\n    'purchasing_sell_national_housing_projects_outside_compound': 'National Housing - Mixed Housing - Youth Housing - Ganet Masr - Dar Masr - Sakan Masr',\n    'purchasing_sell_administrative_commercial_units_outside_compound': 'Administrative Units - Commercial - Pharmacies - Clinics - Shops',\n    'purchasing_sell_industrial_and_warehousing_outside_compound': 'Factories - Warehouses - Industrial Lands - Warehouse Lands',\n    'purchasing_sell_lands_and_ready_projects_outside_compound': 'Administrative & Commercial Lands - Commercial Administrative Malls',\n    'purchasing_sell_villas_and_buildings_outside_compound': 'Villas - Full Buildings - Residential Lands - Concrete Structure',\n    'purchasing_sell_residential_inside_compound': 'Apartments - duplexes - penthouses - i villa - studio',\n    'purchasing_sell_villas_inside_compound': 'Villas - Standalone - town house - twin house',\n    'purchasing_sell_administrative_commercial_units_inside_compound': 'Administrative Units - Commercial - Pharmacies - Clinics - Shops',\n    'rent_residential_inside_compound': 'Apartments - duplexes - penthouses - i villa - villas - studio',\n    'rent_hotel_Units_inside_compound': 'Hotel Units',\n    'rent_administrative_commercial_units_inside_compound': 'Administrative Units - Commercial - Pharmacies - Clinics - Shops',\n    'rent_residential_outside_compound': 'Apartments - duplexes - penthouses - roof - basements - villas - studio',\n    'rent_national_housing_projects_compound': 'National Housing - Mixed Housing - Youth Housing - Ganet Masr - Dar Masr - Sakan Masr',\n    'rent_administrative_commercial_units_outside_compound': 'Administrative Units - Commercial - Pharmacies - Clinics - Shops - full buildings',\n    'rent_industrial_and_warehousing_outside_compound': 'Factories - Warehouses',\n    'rent_hotel_units_outside_compound': 'Hotel Units'\n  };\n  static ɵfac = function BrokerRegistrationStepperComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BrokerRegistrationStepperComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ProjectsService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i3.AuthenticationService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: BrokerRegistrationStepperComponent,\n    selectors: [[\"app-broker-registration-stepper\"]],\n    outputs: {\n      onBack: \"onBack\",\n      onComplete: \"onComplete\"\n    },\n    decls: 20,\n    vars: 14,\n    consts: [[1, \"client-registration-stepper\"], [1, \"stepper-header\"], [1, \"stepper-title\"], [1, \"stepper-progress\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"d-flex\"], [1, \"progress-text\"], [\"type\", \"button\", \"class\", \"back-to-previous\", 3, \"click\", 4, \"ngIf\"], [1, \"stepper-form\", 3, \"formGroup\"], [\"class\", \"step-content\", 4, \"ngIf\"], [\"class\", \"step-content compact-step\", 4, \"ngIf\"], [\"class\", \"step-content success-step\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"back-to-previous\", 3, \"click\"], [1, \"step-content\"], [1, \"step-title\"], [1, \"form-group\"], [\"for\", \"fullName\", 1, \"form-label\"], [1, \"ki-outline\", \"ki-user\"], [1, \"required\"], [\"type\", \"text\", \"id\", \"fullName\", \"formControlName\", \"fullName\", \"placeholder\", \"Enter full name...\", \"pattern\", \"[^0-9]*\", \"title\", \"Name cannot contain numbers\", \"required\", \"\", 1, \"form-control\", 3, \"blur\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"for\", \"input\", 1, \"form-label\"], [1, \"ki-outline\", \"ki-phone\"], [\"type\", \"text\", \"id\", \"input\", \"formControlName\", \"input\", \"placeholder\", \"<EMAIL> or 01123928909\", \"title\", \"Enter a valid email address or phone number\", \"required\", \"\", 1, \"form-control\", 3, \"blur\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-verification\", 3, \"click\", \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", \"role\", \"status\", 4, \"ngIf\"], [1, \"help-text\"], [1, \"contact-link\"], [1, \"invalid-feedback\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"], [1, \"verification-code-section\"], [\"formArrayName\", \"verificationCode\", 1, \"verification-inputs\"], [\"class\", \"code-input\", 4, \"ngFor\", \"ngForOf\"], [1, \"countdown-section\"], [\"class\", \"countdown-text\", 4, \"ngIf\"], [\"class\", \"btn btn-link\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"alert alert-danger mt-3\", \"role\", \"alert\", 4, \"ngIf\"], [1, \"code-input\"], [\"type\", \"text\", \"maxlength\", \"1\", 1, \"verification-input\", 3, \"input\", \"formControlName\"], [1, \"countdown-text\"], [1, \"countdown-timer\"], [1, \"btn\", \"btn-link\", 3, \"click\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\", \"mt-3\"], [1, \"broker-type-section\"], [1, \"broker-type-description\"], [1, \"broker-type-options\"], [1, \"broker-type-card\", 3, \"click\"], [1, \"broker-type-icon\"], [1, \"broker-type-title\"], [1, \"broker-type-desc\"], [1, \"ki-outline\", \"ki-office-bag\"], [1, \"documents-section\"], [1, \"documents-description\"], [1, \"upload-card-container\"], [4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-verification\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-link\", \"skip-button\", 3, \"click\"], [1, \"ki-outline\", \"ki-arrow-right\"], [1, \"card\", \"mb-3\", \"cursor-pointer\"], [\"for\", \"image\", 1, \"card-body\", \"text-center\", \"py-2\"], [1, \"upload-icon\", \"cursor-pointer\"], [1, \"fas\", \"fa-arrow-up\"], [1, \"upload-text\", \"cursor-pointer\"], [\"class\", \"badge bg-success ms-2\", 4, \"ngIf\"], [1, \"upload-subtitle\"], [\"type\", \"file\", \"id\", \"image\", \"accept\", \".png,.jpg,.jpeg\", 1, \"d-none\", 3, \"change\"], [\"for\", \"idFront\", 1, \"card-body\", \"text-center\", \"py-2\"], [\"type\", \"file\", \"id\", \"idFront\", \"accept\", \".png,.jpg,.jpeg,.pdf\", \"multiple\", \"\", 1, \"d-none\", 3, \"change\"], [\"for\", \"idBack\", 1, \"card-body\", \"text-center\", \"py-2\"], [\"type\", \"file\", \"id\", \"idBack\", \"accept\", \".png,.jpg,.jpeg,.pdf\", \"multiple\", \"\", 1, \"d-none\", 3, \"change\"], [1, \"badge\", \"bg-success\", \"ms-2\"], [\"for\", \"commercialRegistryImage\", 1, \"card-body\", \"text-center\", \"py-2\"], [\"type\", \"file\", \"id\", \"commercialRegistryImage\", \"accept\", \".png,.jpg,.jpeg,.pdf\", \"multiple\", \"\", 1, \"d-none\", 3, \"change\"], [\"for\", \"taxCardImage\", 1, \"card-body\", \"text-center\", \"py-2\"], [\"type\", \"file\", \"id\", \"taxCardImage\", \"accept\", \".png,.jpg,.jpeg,.pdf\", \"multiple\", \"\", 1, \"d-none\", 3, \"change\"], [1, \"step-content\", \"compact-step\"], [1, \"areas-section\"], [1, \"form-label\"], [1, \"form-control\", 3, \"change\", \"value\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-control\", 3, \"change\", \"value\", \"disabled\"], [3, \"value\"], [1, \"specialization-collapse\"], [\"class\", \"specialization-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"specialization-item\"], [1, \"specialization-header\"], [1, \"scope-checkbox-label\"], [\"type\", \"checkbox\", 1, \"scope-checkbox\", 3, \"change\", \"checked\", \"disabled\"], [1, \"specialization-name\"], [1, \"collapse-icon\", 3, \"click\"], [\"class\", \"specialization-content\", 4, \"ngIf\"], [1, \"specialization-content\"], [1, \"specialization-checkboxes\"], [\"class\", \"checkbox-label\", 4, \"ngFor\", \"ngForOf\"], [1, \"checkbox-label\"], [\"type\", \"checkbox\", 3, \"change\", \"checked\"], [1, \"checkbox-text\"], [\"for\", \"phone\", 1, \"form-label\"], [\"type\", \"tel\", \"id\", \"phone\", \"formControlName\", \"phone\", \"placeholder\", \"01xxxxxxxxx\", \"required\", \"\", \"autocomplete\", \"tel\", 1, \"form-control\", 3, \"blur\"], [\"for\", \"email\", 1, \"form-label\"], [\"type\", \"email\", \"id\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"<EMAIL>...\", \"autocomplete\", \"email\", 1, \"form-control\", 3, \"blur\"], [\"for\", \"password\", 1, \"form-label\"], [1, \"ki-outline\", \"ki-lock\"], [\"type\", \"password\", \"id\", \"password\", \"formControlName\", \"password\", \"placeholder\", \"********\", \"minlength\", \"8\", \"pattern\", \"^(?=.*[a-z])(?=.*[A-Z])(?=.*\\\\d).{8,}$\", \"title\", \"Password must be at least 8 characters with uppercase, lowercase and number\", \"required\", \"\", \"autocomplete\", \"new-password\", 1, \"form-control\", 3, \"blur\"], [\"for\", \"confirmPassword\", 1, \"form-label\"], [\"type\", \"password\", \"id\", \"confirmPassword\", \"formControlName\", \"password_confirmation\", \"placeholder\", \"********\", \"required\", \"\", \"autocomplete\", \"new-password\", 1, \"form-control\", 3, \"blur\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"id\", \"agreeTerms\", \"formControlName\", \"agreeTerms\", 1, \"form-check-input\"], [\"for\", \"agreeTerms\", 1, \"form-check-label\"], [1, \"step-content\", \"success-step\"], [1, \"success-content\"], [1, \"success-icon\"], [1, \"ki-outline\", \"ki-check-circle\"], [1, \"success-title\"], [1, \"success-message\"], [1, \"success-illustration\"], [\"src\", \"assets/media/login/successfully.png\", \"alt\", \"Success\", 1, \"success-image\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-success-action\", 3, \"routerLink\"], [1, \"additional-info\"], [1, \"info-link\"]],\n    template: function BrokerRegistrationStepperComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\", 2);\n        i0.ɵɵtext(3, \"Broker Registration\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"div\", 3)(5, \"div\", 4);\n        i0.ɵɵelement(6, \"div\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"div\", 6)(8, \"span\", 7);\n        i0.ɵɵtext(9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(10, BrokerRegistrationStepperComponent_button_10_Template, 2, 0, \"button\", 8);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(11, \"form\", 9);\n        i0.ɵɵtemplate(12, BrokerRegistrationStepperComponent_div_12_Template, 24, 11, \"div\", 10)(13, BrokerRegistrationStepperComponent_div_13_Template, 17, 9, \"div\", 10)(14, BrokerRegistrationStepperComponent_div_14_Template, 27, 5, \"div\", 10)(15, BrokerRegistrationStepperComponent_div_15_Template, 18, 2, \"div\", 10)(16, BrokerRegistrationStepperComponent_div_16_Template, 34, 8, \"div\", 11)(17, BrokerRegistrationStepperComponent_div_17_Template, 16, 1, \"div\", 11)(18, BrokerRegistrationStepperComponent_div_18_Template, 46, 22, \"div\", 10)(19, BrokerRegistrationStepperComponent_div_19_Template, 15, 2, \"div\", 12);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(6);\n        i0.ɵɵstyleProp(\"width\", ctx.currentStep / ctx.totalSteps * 100, \"%\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate2(\"Step \", ctx.currentStep, \" of \", ctx.totalSteps, \"\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 0 && ctx.currentStep < 8);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"formGroup\", ctx.registrationForm);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 3);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 4);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 5);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 6);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 7);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 8);\n      }\n    },\n    dependencies: [i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.MinLengthValidator, i1.MaxLengthValidator, i1.PatternValidator, i1.FormGroupDirective, i1.FormControlName, i1.FormArrayName, i5.RouterLink],\n    styles: [\".required[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n  font-weight: bold;\\n  margin-left: 2px;\\n}\\n\\n.alert[_ngcontent-%COMP%] {\\n  padding: 10px 14px;\\n  margin-bottom: 12px;\\n  margin-top: 8px;\\n  border: 1px solid transparent;\\n  border-radius: 6px;\\n  font-size: 13px;\\n  text-align: center;\\n  direction: ltr;\\n  word-wrap: break-word;\\n  word-break: break-word;\\n  white-space: normal;\\n  line-height: 1.4;\\n  max-width: 100%;\\n}\\n.alert.alert-danger[_ngcontent-%COMP%] {\\n  color: #721c24;\\n  background-color: #f8d7da;\\n  border-color: #f5c6cb;\\n}\\n\\n.client-registration-stepper[_ngcontent-%COMP%] {\\n  width: 100%;\\n  direction: ltr;\\n  text-align: left;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .invalid-feedback[_ngcontent-%COMP%] {\\n  text-align: left !important;\\n  direction: ltr !important;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%] {\\n  text-align: center !important;\\n  direction: ltr !important;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-header[_ngcontent-%COMP%] {\\n  margin-bottom: 10px;\\n  text-align: center;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-header[_ngcontent-%COMP%]   .stepper-title[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 1.3rem;\\n  font-weight: 600;\\n  margin-bottom: 15px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-header[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 6px;\\n  background-color: #e9ecef;\\n  border-radius: 3px;\\n  margin-bottom: 10px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-header[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: linear-gradient(90deg, #28a745 0%, #20c997 100%);\\n  border-radius: 3px;\\n  transition: width 0.3s ease;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-header[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 5px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-header[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-header[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .back-to-previous[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #007bff;\\n  font-size: 0.85rem;\\n  cursor: pointer;\\n  padding: 3px 0;\\n  transition: color 0.3s ease;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-header[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .back-to-previous[_ngcontent-%COMP%]:hover {\\n  color: #0056b3;\\n  text-decoration: underline;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%] {\\n  min-height: 50px;\\n  margin-bottom: 5px;\\n  text-align: center;\\n  padding: 2px 5px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]:has(.form-group:nth-child(n+4))   .form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 10px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]:has(.form-group:nth-child(n+4))   .alert[_ngcontent-%COMP%] {\\n  margin-top: 6px;\\n  margin-bottom: 10px;\\n  padding: 8px 12px;\\n  font-size: 12px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content.success-step[_ngcontent-%COMP%] {\\n  min-height: 50px;\\n  padding: 5px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content.compact-step[_ngcontent-%COMP%] {\\n  min-height: 15px;\\n  padding: 3px 8px;\\n  margin-bottom: 8px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content.compact-step[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%] {\\n  margin-bottom: 4px;\\n  font-size: 13px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content.compact-step[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 3px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content.compact-step[_ngcontent-%COMP%]   .btn-verification[_ngcontent-%COMP%] {\\n  margin: 2px auto 1px;\\n  padding: 10px 16px;\\n  font-size: 13px;\\n  min-height: 40px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content.compact-step[_ngcontent-%COMP%]   .skip-button[_ngcontent-%COMP%] {\\n  margin: 1px auto 1px;\\n  padding: 2px 4px;\\n  font-size: 10px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content.compact-step[_ngcontent-%COMP%]   .help-text[_ngcontent-%COMP%] {\\n  margin-top: 1px;\\n  font-size: 9px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content.compact-step[_ngcontent-%COMP%]   .areas-section[_ngcontent-%COMP%]:not(:has(.specialization-grid))   .form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 22px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content.compact-step[_ngcontent-%COMP%]   .areas-section[_ngcontent-%COMP%]:not(:has(.specialization-grid))   .btn-verification[_ngcontent-%COMP%] {\\n  margin: 35px auto 20px;\\n  padding: 12px 16px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content.compact-step[_ngcontent-%COMP%]   .specialization-collapse[_ngcontent-%COMP%] {\\n  width: 130%;\\n  margin-left: -15%;\\n  padding: 4px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content.compact-step[_ngcontent-%COMP%]   .specialization-collapse[_ngcontent-%COMP%]   .specialization-item[_ngcontent-%COMP%] {\\n  margin-bottom: 4px;\\n  border: 1px solid #e9ecef;\\n  border-radius: 6px;\\n  background: #f8f9fa;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content.compact-step[_ngcontent-%COMP%]   .specialization-collapse[_ngcontent-%COMP%]   .specialization-item[_ngcontent-%COMP%]   .specialization-header[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  transition: background-color 0.3s ease;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content.compact-step[_ngcontent-%COMP%]   .specialization-collapse[_ngcontent-%COMP%]   .specialization-item[_ngcontent-%COMP%]   .specialization-header[_ngcontent-%COMP%]:hover:not(.disabled) {\\n  background-color: #e9ecef;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content.compact-step[_ngcontent-%COMP%]   .specialization-collapse[_ngcontent-%COMP%]   .specialization-item[_ngcontent-%COMP%]   .specialization-header.disabled[_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n  pointer-events: none;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content.compact-step[_ngcontent-%COMP%]   .specialization-collapse[_ngcontent-%COMP%]   .specialization-item[_ngcontent-%COMP%]   .specialization-header[_ngcontent-%COMP%]   .scope-checkbox-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  cursor: pointer;\\n  flex: 1;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content.compact-step[_ngcontent-%COMP%]   .specialization-collapse[_ngcontent-%COMP%]   .specialization-item[_ngcontent-%COMP%]   .specialization-header[_ngcontent-%COMP%]   .scope-checkbox-label[_ngcontent-%COMP%]   .scope-checkbox[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  accent-color: #28a745;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content.compact-step[_ngcontent-%COMP%]   .specialization-collapse[_ngcontent-%COMP%]   .specialization-item[_ngcontent-%COMP%]   .specialization-header[_ngcontent-%COMP%]   .scope-checkbox-label[_ngcontent-%COMP%]   .specialization-name[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #28a745;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content.compact-step[_ngcontent-%COMP%]   .specialization-collapse[_ngcontent-%COMP%]   .specialization-item[_ngcontent-%COMP%]   .specialization-header[_ngcontent-%COMP%]   .collapse-icon[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #6c757d;\\n  transition: transform color ease;\\n  cursor: pointer;\\n  padding: 4px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content.compact-step[_ngcontent-%COMP%]   .specialization-collapse[_ngcontent-%COMP%]   .specialization-item[_ngcontent-%COMP%]   .specialization-header[_ngcontent-%COMP%]   .collapse-icon[_ngcontent-%COMP%]:hover {\\n  color: #28a745;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content.compact-step[_ngcontent-%COMP%]   .specialization-collapse[_ngcontent-%COMP%]   .specialization-item[_ngcontent-%COMP%]   .specialization-content[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  border-top: 1px solid #e9ecef;\\n  background: white;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content.compact-step[_ngcontent-%COMP%]   .specialization-collapse[_ngcontent-%COMP%]   .specialization-item[_ngcontent-%COMP%]   .specialization-content[_ngcontent-%COMP%]   .specialization-checkboxes[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 4px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content.compact-step[_ngcontent-%COMP%]   .specialization-collapse[_ngcontent-%COMP%]   .specialization-item[_ngcontent-%COMP%]   .specialization-content[_ngcontent-%COMP%]   .specialization-checkboxes[_ngcontent-%COMP%]   .checkbox-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  padding: 2px;\\n  font-size: 14px;\\n  line-height: 1.2;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content.compact-step[_ngcontent-%COMP%]   .specialization-collapse[_ngcontent-%COMP%]   .specialization-item[_ngcontent-%COMP%]   .specialization-content[_ngcontent-%COMP%]   .specialization-checkboxes[_ngcontent-%COMP%]   .checkbox-label[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%] {\\n  width: 14px;\\n  height: 14px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%] {\\n  color: #232176;\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  margin-bottom: 5px;\\n  text-align: center;\\n  padding-bottom: 3px;\\n}\\n.compact-step[_ngcontent-%COMP%]   .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  margin-bottom: 3px;\\n  padding-bottom: 2px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  margin-bottom: 6px;\\n  font-weight: 500;\\n  color: #333;\\n  font-size: 0.9rem;\\n  text-align: left;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  font-size: 1rem;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 8px 12px;\\n  border: 1px solid #ddd;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  transition: all 0.3s ease;\\n  background-color: #fff;\\n  margin-bottom: 5px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #28a745;\\n  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-control.is-invalid[_ngcontent-%COMP%] {\\n  border-color: #dc3545;\\n  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]::placeholder {\\n  color: #999;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .invalid-feedback[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #dc3545;\\n  font-size: 12px;\\n  margin-top: 3px;\\n  margin-bottom: 2px;\\n  font-weight: 500;\\n  text-align: left;\\n  direction: ltr;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  text-align: left;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%] {\\n  margin-top: 3px;\\n  width: 18px;\\n  height: 18px;\\n  cursor: pointer;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .form-check-input.is-invalid[_ngcontent-%COMP%] {\\n  border-color: #dc3545;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .form-check-label[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  font-size: 14px;\\n  line-height: 1.4;\\n  color: #333;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .verification-code-section[_ngcontent-%COMP%] {\\n  margin: 15px 0;\\n  text-align: center;\\n  padding: 0 5px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .verification-code-section[_ngcontent-%COMP%]   .verification-inputs[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: 10px;\\n  margin-bottom: 8px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .verification-code-section[_ngcontent-%COMP%]   .verification-inputs[_ngcontent-%COMP%]   .verification-input[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  text-align: center;\\n  font-size: 1.5rem;\\n  font-weight: bold;\\n  border: 2px solid #ddd;\\n  border-radius: 8px;\\n  background-color: #fff;\\n  color: #333;\\n  transition: all 0.3s ease;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .verification-code-section[_ngcontent-%COMP%]   .verification-inputs[_ngcontent-%COMP%]   .verification-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #28a745;\\n  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .verification-code-section[_ngcontent-%COMP%]   .verification-inputs[_ngcontent-%COMP%]   .verification-input.is-invalid[_ngcontent-%COMP%] {\\n  border-color: #dc3545;\\n  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .verification-code-section[_ngcontent-%COMP%]   .verification-inputs[_ngcontent-%COMP%]   .verification-input[_ngcontent-%COMP%]::-webkit-outer-spin-button, .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .verification-code-section[_ngcontent-%COMP%]   .verification-inputs[_ngcontent-%COMP%]   .verification-input[_ngcontent-%COMP%]::-webkit-inner-spin-button {\\n  appearance: none;\\n  margin: 0;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .verification-code-section[_ngcontent-%COMP%]   .verification-inputs[_ngcontent-%COMP%]   .verification-input[type=number][_ngcontent-%COMP%] {\\n  appearance: textfield;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .countdown-section[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 8px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .countdown-text[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 13px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .countdown-text[_ngcontent-%COMP%]   .countdown-timer[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  font-weight: bold;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .documents-section[_ngcontent-%COMP%], \\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .areas-section[_ngcontent-%COMP%], \\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .broker-type-section[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .documents-section[_ngcontent-%COMP%]   .documents-description[_ngcontent-%COMP%], \\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .documents-section[_ngcontent-%COMP%]   .areas-description[_ngcontent-%COMP%], \\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .documents-section[_ngcontent-%COMP%]   .broker-type-description[_ngcontent-%COMP%], \\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .areas-section[_ngcontent-%COMP%]   .documents-description[_ngcontent-%COMP%], \\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .areas-section[_ngcontent-%COMP%]   .areas-description[_ngcontent-%COMP%], \\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .areas-section[_ngcontent-%COMP%]   .broker-type-description[_ngcontent-%COMP%], \\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .broker-type-section[_ngcontent-%COMP%]   .documents-description[_ngcontent-%COMP%], \\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .broker-type-section[_ngcontent-%COMP%]   .areas-description[_ngcontent-%COMP%], \\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .broker-type-section[_ngcontent-%COMP%]   .broker-type-description[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  font-size: 13px;\\n  line-height: 1.4;\\n  margin-bottom: 12px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .upload-card-container[_ngcontent-%COMP%] {\\n  margin-bottom: 6px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .upload-card-container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%] {\\n  border: 2px dashed #e9ecef;\\n  border-radius: 6px;\\n  background: #f8f9fa;\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .upload-card-container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]:hover {\\n  border-color: #4c63d2;\\n  background: #f0f4ff;\\n  box-shadow: 0 2px 8px rgba(76, 99, 210, 0.15);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .upload-card-container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]:hover:hover {\\n  transform: translateY(-1px);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .upload-card-container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n  text-align: center;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .upload-card-container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]   .upload-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  color: #4c63d2;\\n  margin-bottom: 4px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .upload-card-container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]   .upload-text[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #333;\\n  font-size: 12px;\\n  font-weight: 600;\\n  margin-bottom: 3px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .upload-card-container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]   .upload-text[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  padding: 2px 6px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .upload-card-container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]   .upload-subtitle[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 10px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .skip-button[_ngcontent-%COMP%] {\\n  border: none;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  background: none;\\n  color: #007bff;\\n  font-size: 14px;\\n  font-weight: 500;\\n  margin: 12px auto 0;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n  text-decoration: none;\\n  gap: 10px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .skip-button[_ngcontent-%COMP%]:hover {\\n  color: #0056b3;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .skip-button[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  box-shadow: none;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .skip-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  margin-left: 4px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .dropdown[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .dropdown[_ngcontent-%COMP%]   .dropdown-toggle[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);\\n  border-color: #28a745;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .dropdown[_ngcontent-%COMP%]   .dropdown-toggle[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .dropdown[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%] {\\n  display: none;\\n  border: 1px solid #e9ecef;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n  background-color: white;\\n  margin-top: 2px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .dropdown[_ngcontent-%COMP%]   .dropdown-menu.show[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .dropdown[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  transition: background-color 0.3s ease;\\n  cursor: pointer;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .dropdown[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]:hover:not(.disabled) {\\n  background-color: rgba(40, 167, 69, 0.1);\\n  color: #28a745;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .dropdown[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-item.disabled[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  background-color: transparent;\\n  cursor: default;\\n  font-size: 12px;\\n  font-style: italic;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 8px;\\n  margin-top: 12px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%] {\\n  width: 18px;\\n  height: 18px;\\n  cursor: pointer;\\n  margin-top: 3px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .form-check-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #333;\\n  cursor: pointer;\\n  line-height: 1.4;\\n  text-align: left;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 20px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   .success-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  color: #28a745;\\n  margin-bottom: 20px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   .success-title[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  margin-bottom: 15px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   .success-message[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 1rem;\\n  line-height: 1.5;\\n  margin-bottom: 20px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   .success-image[_ngcontent-%COMP%] {\\n  max-width: 200px;\\n  height: auto;\\n  margin: 20px 0;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   .btn-success-action[_ngcontent-%COMP%] {\\n  border: none;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\\n  color: white;\\n  padding: 12px 24px;\\n  font-size: 14px;\\n  margin: 15px auto;\\n  display: block;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   .btn-success-action[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   .btn-success-action[_ngcontent-%COMP%]:hover:hover {\\n  transform: translateY(-1px);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   .info-link[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  font-size: 0.9rem;\\n  cursor: pointer;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   .info-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .broker-type-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n  justify-content: center;\\n  margin-bottom: 20px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .broker-type-options[_ngcontent-%COMP%]   .broker-type-card[_ngcontent-%COMP%] {\\n  flex: 1;\\n  max-width: 200px;\\n  padding: 20px 15px;\\n  border: 2px solid #e9ecef;\\n  border-radius: 12px;\\n  background: #f8f9fa;\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  text-align: center;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .broker-type-options[_ngcontent-%COMP%]   .broker-type-card[_ngcontent-%COMP%]:hover {\\n  border-color: #4c63d2;\\n  background: #f0f4ff;\\n  box-shadow: 0 2px 8px rgba(76, 99, 210, 0.15);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .broker-type-options[_ngcontent-%COMP%]   .broker-type-card[_ngcontent-%COMP%]:hover:hover {\\n  transform: translateY(-1px);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .broker-type-options[_ngcontent-%COMP%]   .broker-type-card.selected[_ngcontent-%COMP%] {\\n  border-color: #4c63d2;\\n  background: #f0f4ff;\\n  box-shadow: 0 4px 12px rgba(76, 99, 210, 0.2);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .broker-type-options[_ngcontent-%COMP%]   .broker-type-card.selected[_ngcontent-%COMP%]   .broker-type-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], \\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .broker-type-options[_ngcontent-%COMP%]   .broker-type-card.selected[_ngcontent-%COMP%]   .broker-type-title[_ngcontent-%COMP%] {\\n  color: #4c63d2;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .broker-type-options[_ngcontent-%COMP%]   .broker-type-card[_ngcontent-%COMP%]   .broker-type-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  color: #6c757d;\\n  margin-bottom: 10px;\\n  transition: color 0.3s ease;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .broker-type-options[_ngcontent-%COMP%]   .broker-type-card[_ngcontent-%COMP%]   .broker-type-title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #333;\\n  margin-bottom: 8px;\\n  transition: color 0.3s ease;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .broker-type-options[_ngcontent-%COMP%]   .broker-type-card[_ngcontent-%COMP%]   .broker-type-desc[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #6c757d;\\n  line-height: 1.4;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .btn-verification[_ngcontent-%COMP%] {\\n  border: none;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  width: 100%;\\n  padding: 12px 16px;\\n  font-size: 14px;\\n  font-weight: 600;\\n  border-radius: 20px;\\n  margin: 25px auto 15px;\\n  background: linear-gradient(135deg, #4c63d2 0%, #3b4db8 100%);\\n  color: white;\\n  text-align: center;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .btn-verification[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  box-shadow: 0 4px 12px rgba(76, 99, 210, 0.3);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .btn-verification[_ngcontent-%COMP%]:hover:not(:disabled):hover {\\n  transform: translateY(-1px);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .btn-verification[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.7;\\n  cursor: not-allowed;\\n  transform: none;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .btn-verification.loading[_ngcontent-%COMP%] {\\n  position: relative;\\n  pointer-events: none;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .btn-verification.loading[_ngcontent-%COMP%]   .spinner-border[_ngcontent-%COMP%] {\\n  width: 1rem;\\n  height: 1rem;\\n  border-width: 0.125em;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .btn-verification[_ngcontent-%COMP%]   .spinner-border[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  width: 1rem;\\n  height: 1rem;\\n  vertical-align: text-bottom;\\n  border: 0.125em solid currentColor;\\n  border-right-color: transparent;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spinner-border 0.75s linear infinite;\\n}\\n@keyframes _ngcontent-%COMP%_spinner-border {\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .help-text[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 8px;\\n  font-size: 12px;\\n  color: #6c757d;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .help-text[_ngcontent-%COMP%]   .contact-link[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  cursor: pointer;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .help-text[_ngcontent-%COMP%]   .contact-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .specialization-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 20px;\\n  margin: 15px 0;\\n  padding: 25px;\\n  border: 1px solid #e9ecef;\\n  border-radius: 10px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .specialization-grid[_ngcontent-%COMP%]   .specialization-card[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  border: 1px solid #e9ecef;\\n  border-radius: 8px;\\n  padding: 20px;\\n  transition: all 0.3s ease;\\n  min-height: 120px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .specialization-grid[_ngcontent-%COMP%]   .specialization-card[_ngcontent-%COMP%]:hover {\\n  border-color: #28a745;\\n  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.1);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .specialization-grid[_ngcontent-%COMP%]   .specialization-card[_ngcontent-%COMP%]   .specialization-title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #28a745;\\n  margin-bottom: 12px;\\n  text-align: center;\\n  border-bottom: 1px solid #e9ecef;\\n  padding-bottom: 8px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .specialization-grid[_ngcontent-%COMP%]   .specialization-card[_ngcontent-%COMP%]   .specialization-checkboxes[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 8px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .specialization-grid[_ngcontent-%COMP%]   .specialization-card[_ngcontent-%COMP%]   .specialization-checkboxes[_ngcontent-%COMP%]   .checkbox-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  padding: 6px;\\n  transition: color 0.3s ease;\\n  font-size: 14px;\\n  white-space: nowrap;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .specialization-grid[_ngcontent-%COMP%]   .specialization-card[_ngcontent-%COMP%]   .specialization-checkboxes[_ngcontent-%COMP%]   .checkbox-label[_ngcontent-%COMP%]:hover {\\n  color: #28a745;\\n  background-color: rgba(40, 167, 69, 0.05);\\n  border-radius: 4px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .specialization-grid[_ngcontent-%COMP%]   .specialization-card[_ngcontent-%COMP%]   .specialization-checkboxes[_ngcontent-%COMP%]   .checkbox-label[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  cursor: pointer;\\n  accent-color: #28a745;\\n  flex-shrink: 0;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .specialization-grid[_ngcontent-%COMP%]   .specialization-card[_ngcontent-%COMP%]   .specialization-checkboxes[_ngcontent-%COMP%]   .checkbox-label[_ngcontent-%COMP%]   .checkbox-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #495057;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "Validators", "i0", "ɵɵelementStart", "ɵɵlistener", "BrokerRegistrationStepperComponent_button_10_Template_button_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "previousStep", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "getFieldError", "ɵɵelement", "BrokerRegistrationStepperComponent_div_12_Template_input_blur_8_listener", "_r3", "mark<PERSON>ieldAsTouched", "ɵɵtemplate", "BrokerRegistrationStepperComponent_div_12_div_9_Template", "BrokerRegistrationStepperComponent_div_12_Template_input_blur_15_listener", "BrokerRegistrationStepperComponent_div_12_div_16_Template", "BrokerRegistrationStepperComponent_div_12_Template_button_click_17_listener", "handleNextStepAndSendCode", "BrokerRegistrationStepperComponent_div_12_span_18_Template", "ɵɵclassProp", "isFieldInvalid", "ɵɵproperty", "isLoadingSendOtp", "isStep1Valid", "BrokerRegistrationStepperComponent_div_13_div_5_Template_input_input_1_listener", "$event", "i_r6", "_r5", "index", "autoFocusNext", "ctrl_r7", "invalid", "touched", "countdown", "BrokerRegistrationStepperComponent_div_13_button_8_Template_button_click_0_listener", "_r8", "onResendCode", "otpErrorMessage", "BrokerRegistrationStepperComponent_div_13_div_5_Template", "BrokerRegistrationStepperComponent_div_13_span_7_Template", "BrokerRegistrationStepperComponent_div_13_button_8_Template", "BrokerRegistrationStepperComponent_div_13_div_9_Template", "BrokerRegistrationStepperComponent_div_13_Template_button_click_10_listener", "_r4", "checkOTP", "BrokerRegistrationStepperComponent_div_13_span_11_Template", "verificationCodeControls", "showResendButton", "isLoadingCheckOtp", "isStep2Valid", "BrokerRegistrationStepperComponent_div_14_Template_div_click_7_listener", "_r9", "selectBrokerType", "BrokerRegistrationStepperComponent_div_14_Template_div_click_14_listener", "BrokerRegistrationStepperComponent_div_14_Template_button_click_21_listener", "nextStep", "brokerType", "getFileCount", "BrokerRegistrationStepperComponent_div_15_div_7_span_7_Template", "BrokerRegistrationStepperComponent_div_15_div_7_Template_input_change_10_listener", "_r11", "onFileChange", "BrokerRegistrationStepperComponent_div_15_div_7_span_17_Template", "BrokerRegistrationStepperComponent_div_15_div_7_Template_input_change_20_listener", "BrokerRegistrationStepperComponent_div_15_div_7_span_27_Template", "BrokerRegistrationStepperComponent_div_15_div_7_Template_input_change_30_listener", "BrokerRegistrationStepperComponent_div_15_div_8_span_7_Template", "BrokerRegistrationStepperComponent_div_15_div_8_Template_input_change_10_listener", "_r12", "BrokerRegistrationStepperComponent_div_15_div_8_span_17_Template", "BrokerRegistrationStepperComponent_div_15_div_8_Template_input_change_20_listener", "BrokerRegistrationStepperComponent_div_15_div_8_span_27_Template", "BrokerRegistrationStepperComponent_div_15_div_8_Template_input_change_30_listener", "BrokerRegistrationStepperComponent_div_15_div_7_Template", "BrokerRegistrationStepperComponent_div_15_div_8_Template", "BrokerRegistrationStepperComponent_div_15_Template_button_click_9_listener", "_r10", "BrokerRegistrationStepperComponent_div_15_Template_button_click_11_listener", "city_r14", "id", "name_en", "area_r15", "subarea_r16", "ɵɵtextInterpolate", "BrokerRegistrationStepperComponent_div_16_Template_select_change_7_listener", "_r13", "onCityChange", "BrokerRegistrationStepperComponent_div_16_option_10_Template", "BrokerRegistrationStepperComponent_div_16_Template_select_change_14_listener", "onAreaChange", "BrokerRegistrationStepperComponent_div_16_option_17_Template", "BrokerRegistrationStepperComponent_div_16_Template_select_change_21_listener", "onSubAreaChange", "BrokerRegistrationStepperComponent_div_16_option_24_Template", "BrokerRegistrationStepperComponent_div_16_Template_button_click_25_listener", "BrokerRegistrationStepperComponent_div_16_Template_button_click_27_listener", "nextStepSkipping", "selectedCityId", "cities", "selectedAreaId", "areas", "selectedSubAreaId", "subAreas", "BrokerRegistrationStepperComponent_div_17_div_6_div_7_label_2_Template_input_change_1_listener", "type_r21", "_r20", "$implicit", "scope_r19", "onSpecializationChange", "selectedTypes", "includes", "specializationDisplayMap", "BrokerRegistrationStepperComponent_div_17_div_6_div_7_label_2_Template", "specializations", "BrokerRegistrationStepperComponent_div_17_div_6_Template_input_change_3_listener", "_r18", "onScopeSelectionChange", "BrokerRegistrationStepperComponent_div_17_div_6_Template_i_click_6_listener", "toggleSpecializationScope", "BrokerRegistrationStepperComponent_div_17_div_6_div_7_Template", "isOtherScopeExpanded", "selected", "specialization_scope", "ɵɵclassMap", "expanded", "BrokerRegistrationStepperComponent_div_17_div_6_Template", "BrokerRegistrationStepperComponent_div_17_Template_button_click_7_listener", "_r17", "BrokerRegistrationStepperComponent_div_17_Template_button_click_9_listener", "staticScopes", "getFormError", "createAccountErrorMessage", "BrokerRegistrationStepperComponent_div_18_Template_input_blur_8_listener", "_r22", "BrokerRegistrationStepperComponent_div_18_div_9_Template", "BrokerRegistrationStepperComponent_div_18_Template_input_blur_14_listener", "BrokerRegistrationStepperComponent_div_18_div_15_Template", "BrokerRegistrationStepperComponent_div_18_Template_input_blur_21_listener", "BrokerRegistrationStepperComponent_div_18_div_22_Template", "BrokerRegistrationStepperComponent_div_18_Template_input_blur_28_listener", "BrokerRegistrationStepperComponent_div_18_div_29_Template", "BrokerRegistrationStepperComponent_div_18_div_30_Template", "BrokerRegistrationStepperComponent_div_18_div_37_Template", "BrokerRegistrationStepperComponent_div_18_div_38_Template", "BrokerRegistrationStepperComponent_div_18_Template_button_click_39_listener", "createAccount", "BrokerRegistrationStepperComponent_div_18_span_40_Template", "isLoadingCreateAccount", "isStep7Valid", "ɵɵpureFunction0", "_c0", "SPECIALIZATION_MAPPING", "purchase_sell_outside_compound", "primary_inside_compound", "resale_inside_compound", "rentals_outside_compound", "rentals_inside_compound", "BrokerRegistrationStepperComponent", "fb", "projectsService", "cd", "authenticationService", "onBack", "onComplete", "registrationForm", "currentStep", "totalSteps", "uploadedFiles", "verificationDigits", "noNumbers", "pattern", "emailOrPhonePattern", "phonePattern", "passwordPattern", "selectedCityName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectedSubAreaName", "step2Form", "constructor", "createForm", "group", "cityId", "areaId", "subAreaId", "initializeSpecializations", "loadCities", "fullName", "required", "<PERSON><PERSON><PERSON><PERSON>", "input", "verificationCode", "array", "Array", "fill", "map", "control", "phone", "email", "password", "password_confirmation", "agreeTerms", "requiredTrue", "Object", "entries", "scope", "emit", "event", "fileType", "files", "target", "length", "from", "console", "log", "clearFiles", "clearAllFiles", "getCities", "subscribe", "next", "response", "data", "warn", "error", "err", "complete", "detectChanges", "loadAreas", "<PERSON><PERSON><PERSON><PERSON>", "loadSubAreas", "getSubAreas", "value", "options", "selectedIndex", "text", "patchValue", "for<PERSON>ach", "s", "checked", "type", "push", "filter", "t", "getSpecializationScopesPayload", "currentScope", "some", "get", "controls", "onDigitInput", "code", "join", "fieldName", "field", "dirty", "<PERSON><PERSON><PERSON><PERSON>ched", "errors", "<PERSON><PERSON><PERSON><PERSON>", "confirmPassword", "valid", "passwordConfirmation", "passwordsMatch", "sendVerificationCode", "moveToNextStep", "trim", "params", "isEmail", "test", "isPhone", "sendOtp", "startCountdown", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "message", "verificationCodeArray", "codeArray", "otp", "checkOtp", "role", "specializationParams", "formData", "FormData", "key", "hasOwnProperty", "append", "areaIds", "values", "file", "register", "user", "localStorage", "setItem", "authToken", "setCurrentUser", "intervalId", "setInterval", "clearInterval", "nextInput", "parentElement", "nextElement<PERSON><PERSON>ling", "querySelector", "focus", "clearOtpInputs", "setValue", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mark<PERSON><PERSON>ristine", "toSnakeCase", "str", "toLowerCase", "replace", "result", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ProjectsService", "ChangeDetectorRef", "i3", "AuthenticationService", "selectors", "outputs", "decls", "vars", "consts", "template", "BrokerRegistrationStepperComponent_Template", "rf", "ctx", "BrokerRegistrationStepperComponent_button_10_Template", "BrokerRegistrationStepperComponent_div_12_Template", "BrokerRegistrationStepperComponent_div_13_Template", "BrokerRegistrationStepperComponent_div_14_Template", "BrokerRegistrationStepperComponent_div_15_Template", "BrokerRegistrationStepperComponent_div_16_Template", "BrokerRegistrationStepperComponent_div_17_Template", "BrokerRegistrationStepperComponent_div_18_Template", "BrokerRegistrationStepperComponent_div_19_Template", "ɵɵstyleProp", "ɵɵtextInterpolate2"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\authentication\\components\\broker-registration-stepper\\broker-registration-stepper.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\authentication\\components\\broker-registration-stepper\\broker-registration-stepper.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  Output,\r\n  EventEmitter,\r\n  ChangeDetectorRef,\r\n} from '@angular/core';\r\nimport { FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';\r\nimport { BrokerRegistrationData } from '../../models';\r\nimport { ProjectsService } from '../../../developer/services/projects.service';\r\nimport { AuthenticationService } from '../../services/authentication.service';\r\n\r\n\r\n\r\nconst SPECIALIZATION_MAPPING: Record<string, string[]> = {\r\n  purchase_sell_outside_compound: [\r\n    'purchasing_sell_residential_outside_compound',\r\n    'purchasing_sell_national_housing_projects_outside_compound',\r\n    'purchasing_sell_administrative_commercial_units_outside_compound',\r\n    'purchasing_sell_industrial_and_warehousing_outside_compound',\r\n    'purchasing_sell_lands_and_ready_projects_outside_compound',\r\n    'purchasing_sell_villas_and_buildings_outside_compound',\r\n  ],\r\n  // purchase_sell_inside_compound: [\r\n  //   'purchasing_sell_residential_inside_compound',\r\n  //   'purchasing_sell_villas_inside_compound',\r\n  //   'purchasing_sell_administrative_commercial_Units_inside_compound',\r\n  // ],\r\n  primary_inside_compound: [\r\n    'purchasing_sell_residential_inside_compound',\r\n    'purchasing_sell_villas_inside_compound',\r\n    'purchasing_sell_administrative_commercial_units_inside_compound',\r\n  ],\r\n  resale_inside_compound: [\r\n    'purchasing_sell_residential_inside_compound',\r\n    'purchasing_sell_villas_inside_compound',\r\n    'purchasing_sell_administrative_commercial_units_inside_compound',\r\n  ],\r\n  rentals_outside_compound: [\r\n    'rent_residential_outside_compound',\r\n    'rent_national_housing_projects_outside_compound',\r\n    'rent_administrative_commercial_units_outside_compound',\r\n    'rent_industrial_and_warehousing_outside_compound',\r\n    'rent_hotel_Units_outside_compound'\r\n  ],\r\n  rentals_inside_compound: [\r\n    'rent_residential_inside_compound',\r\n    'rent_hotel_Units_inside_compound',\r\n    'rent_administrative_commercial_units_inside_compound',\r\n  ],\r\n};\r\n\r\n\r\ninterface SpecializationScope {\r\n  specialization_scope: string;\r\n  specializations: string[];\r\n  expanded: boolean;\r\n  selected: boolean;\r\n  selectedTypes?: string[];\r\n}\r\n\r\n@Component({\r\n  selector: 'app-broker-registration-stepper',\r\n  templateUrl: './broker-registration-stepper.component.html',\r\n  styleUrls: ['./broker-registration-stepper.component.scss'],\r\n})\r\nexport class BrokerRegistrationStepperComponent {\r\n  @Output() onBack = new EventEmitter<void>();\r\n  @Output() onComplete = new EventEmitter<BrokerRegistrationData>();\r\n\r\n  registrationForm: FormGroup;\r\n  currentStep = 1;\r\n  readonly totalSteps = 8;\r\n  uploadedFiles: { [key: string]: File[] } = {};\r\n  brokerType = '';\r\n  verificationDigits: string[] = ['', '', '', '', ''];\r\n  countdown: number = 25;\r\n  showResendButton: boolean = false;\r\n  isLoadingSendOtp: boolean = false;\r\n  isLoadingCheckOtp: boolean = false;\r\n  isLoadingCreateAccount: boolean = false;\r\n  otpErrorMessage: string = '';\r\n  createAccountErrorMessage: string = '';\r\n\r\n  // Validators\r\n  static noNumbers = Validators.pattern(/^[^0-9]*$/);\r\n  static emailOrPhonePattern = Validators.pattern(/^([^\\s@]+@[^\\s@]+\\.[^\\s@]+|01[0125]\\d{8}|05\\d{8}|\\+201[0125]\\d{8}|\\+9665\\d{8})$/);\r\n  static phonePattern = Validators.pattern(/^(01[0125]\\d{8}|05\\d{8}|\\+201[0125]\\d{8}|\\+9665\\d{8})$/);\r\n  static passwordPattern = Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/);\r\n\r\n  // Location data\r\n  cities: any[] = [];\r\n  areas: any[] = [];\r\n  subAreas: any[] = [];\r\n  selectedCityId: number = 0;\r\n  selectedCityName = '';\r\n  selectedAreaId: number = 0;\r\n  selectedAreaName = '';\r\n  selectedSubAreaId: number = 0;\r\n  selectedSubAreaName = '';\r\n  step2Form: FormGroup;\r\n\r\n  // Specializations\r\n  staticScopes: SpecializationScope[] = [];\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private projectsService: ProjectsService,\r\n    private cd: ChangeDetectorRef,\r\n    private authenticationService: AuthenticationService\r\n  ) {\r\n    this.registrationForm = this.createForm();\r\n\r\n    this.step2Form = this.fb.group({\r\n      cityId: [0],\r\n      areaId: [0],\r\n      subAreaId:[0],\r\n    });\r\n\r\n    this.initializeSpecializations();\r\n    this.loadCities();\r\n  }\r\n\r\n  private createForm(): FormGroup {\r\n    return this.fb.group({\r\n      // Step 1: Basic Information\r\n      fullName: [\r\n        '',\r\n        [\r\n          Validators.required,\r\n          Validators.minLength(2),\r\n          BrokerRegistrationStepperComponent.noNumbers,\r\n        ],\r\n      ],\r\n      input: [\r\n        '',\r\n        [Validators.required, BrokerRegistrationStepperComponent.emailOrPhonePattern],\r\n      ],\r\n\r\n      // Step 2: Verification Code\r\n      verificationCode: this.fb.array(\r\n        Array(5)\r\n          .fill('')\r\n          .map(() =>\r\n            this.fb.control('', [\r\n              Validators.required,\r\n              Validators.pattern('[0-9]'),\r\n            ])\r\n          )\r\n      ),\r\n\r\n      // Step 7: Phone, Email, Password, Terms\r\n      phone: ['', [Validators.required, BrokerRegistrationStepperComponent.phonePattern]],\r\n      email: ['', [Validators.email]],\r\n      password: [\r\n        '',\r\n        [\r\n          Validators.required,\r\n          Validators.minLength(8),\r\n          BrokerRegistrationStepperComponent.passwordPattern,\r\n        ],\r\n      ],\r\n      password_confirmation: ['', [Validators.required]],\r\n      agreeTerms: [false, [Validators.requiredTrue]],\r\n    });\r\n  }\r\n\r\n  private initializeSpecializations(): void {\r\n      this.staticScopes = Object.entries(SPECIALIZATION_MAPPING).map(([scope, specializations]) => ({\r\n      specialization_scope: scope,\r\n      specializations,\r\n      expanded: false,\r\n      selected: false,\r\n      selectedTypes: [],\r\n    }));\r\n  }\r\n\r\n  nextStep(): void {\r\n    if (this.currentStep < this.totalSteps) {\r\n      // Clear OTP error message when leaving step 2\r\n      if (this.currentStep === 2) {\r\n        this.otpErrorMessage = '';\r\n      }\r\n      this.currentStep++;\r\n    }\r\n  }\r\n\r\n  nextStepSkipping(): void {\r\n    if (this.currentStep < this.totalSteps) {\r\n      // Clear OTP error message when leaving step 2\r\n      if (this.currentStep === 2) {\r\n        this.otpErrorMessage = '';\r\n      }\r\n\r\n      if(this.currentStep == 5){\r\n        this.currentStep = 6;\r\n      }\r\n      this.currentStep++;\r\n    }\r\n  }\r\n\r\n  previousStep(): void {\r\n    if (this.currentStep > 1) {\r\n      this.currentStep--;\r\n    } else {\r\n      this.onBack.emit();\r\n    }\r\n  }\r\n\r\n  onFileChange(event: Event, fileType: string): void {\r\n    const files = (event.target as HTMLInputElement).files;\r\n    if (!files?.length) return;\r\n\r\n    // Clear previous files and add new ones (replace instead of append)\r\n    this.uploadedFiles[fileType] = Array.from(files);\r\n    console.log(`Uploaded ${files.length} file(s) for ${fileType} (replaced previous files)`);\r\n  }\r\n\r\n  getFileCount(fileType: string): number {\r\n    return this.uploadedFiles[fileType]?.length || 0;\r\n  }\r\n\r\n  // Clear files for a specific file type\r\n  clearFiles(fileType: string): void {\r\n    delete this.uploadedFiles[fileType];\r\n    console.log(`Cleared all files for ${fileType}`);\r\n  }\r\n\r\n  // Clear all uploaded files\r\n  clearAllFiles(): void {\r\n    this.uploadedFiles = {};\r\n    console.log('Cleared all uploaded files');\r\n  }\r\n\r\n  // Location Methods\r\n  loadCities(): void {\r\n    this.projectsService.getCities().subscribe({\r\n      next: (response: any) => {\r\n        if (response && response.data) {\r\n          this.cities = response.data;\r\n        } else {\r\n          console.warn('No cities data in response');\r\n          this.cities = [];\r\n        }\r\n      },\r\n      error: (err: any) => {\r\n        console.error('Error loading cities:', err);\r\n      },\r\n      complete: () => {\r\n        this.cd.detectChanges();\r\n      },\r\n    });\r\n  }\r\n\r\n  loadAreas(cityId?: number): void {\r\n    this.projectsService.getAreas(cityId).subscribe({\r\n      next: (response: any) => {\r\n        if (response && response.data) {\r\n          this.areas = response.data;\r\n        } else {\r\n          console.warn('No areas data in response');\r\n          this.areas = [];\r\n        }\r\n      },\r\n      error: (err: any) => {\r\n        console.error('Error loading areas:', err);\r\n        this.areas = [];\r\n      },\r\n      complete: () => {\r\n        this.cd.detectChanges();\r\n      },\r\n    });\r\n  }\r\n\r\n  loadSubAreas(areaId?: number): void {\r\n    this.projectsService.getSubAreas(areaId).subscribe({\r\n      next: (response: any) => {\r\n        if (response && response.data) {\r\n          this.subAreas = response.data;\r\n        } else {\r\n          console.warn('No areas data in response');\r\n          this.subAreas = [];\r\n        }\r\n      },\r\n      error: (err: any) => {\r\n        console.error('Error loading areas:', err);\r\n        this.subAreas = [];\r\n      },\r\n      complete: () => {\r\n        this.cd.detectChanges();\r\n      },\r\n    });\r\n  }\r\n\r\n  onCityChange(event: any): void {\r\n    const cityId = event.target.value;\r\n    this.selectedCityId = cityId;\r\n    this.selectedCityName =\r\n      event.target.options[event.target.selectedIndex].text;\r\n    this.step2Form.patchValue({ cityId: cityId });\r\n\r\n    // Reset area\r\n    this.selectedAreaId = 0;\r\n    this.selectedAreaName = '';\r\n    this.step2Form.patchValue({ areaId: 0 });\r\n    this.areas = [];\r\n\r\n    this.loadAreas(cityId);\r\n  }\r\n\r\n  onAreaChange(event: any): void {\r\n    const areaId = event.target.value;\r\n    this.selectedAreaId = areaId;\r\n    this.selectedAreaName =\r\n      event.target.options[event.target.selectedIndex].text;\r\n    this.step2Form.patchValue({ areaId: areaId });\r\n\r\n    this.loadSubAreas(areaId);\r\n  }\r\n\r\n   onSubAreaChange(event: any): void {\r\n    const subAreaId = event.target.value;\r\n    this.selectedSubAreaId = subAreaId;\r\n    this.selectedSubAreaName = event.target.options[event.target.selectedIndex].text;\r\n    this.step2Form.patchValue({ subAreaId: subAreaId });\r\n  }\r\n\r\n  // Specialization Methods\r\n  toggleSpecializationScope(scope: SpecializationScope): void {\r\n    this.staticScopes.forEach((s) => {\r\n      if (s !== scope) s.expanded = false;\r\n    });\r\n    scope.expanded = !scope.expanded;\r\n  }\r\n\r\n  onScopeSelectionChange(scope: SpecializationScope, event: any): void {\r\n    scope.selected = event.target.checked;\r\n    console.log(scope);\r\n  }\r\n\r\n  onSpecializationChange(scope: SpecializationScope, type: string, event: any): void {\r\n    if (!scope.selectedTypes) {\r\n      scope.selectedTypes = [];\r\n    }\r\n\r\n    if (event.target.checked) {\r\n      if (!scope.selectedTypes.includes(type)) {\r\n        scope.selectedTypes.push(type);\r\n      }\r\n    } else {\r\n      scope.selectedTypes = scope.selectedTypes.filter(t => t !== type);\r\n    }\r\n\r\n    console.log(this.getSpecializationScopesPayload());\r\n  }\r\n\r\n  // Utility Methods\r\n  isOtherScopeExpanded(currentScope: SpecializationScope): boolean {\r\n    return this.staticScopes.some(\r\n      (scope) => scope !== currentScope && scope.expanded\r\n    );\r\n  }\r\n\r\n  // Simple action methods\r\n  selectBrokerType(type: string): void {\r\n    this.brokerType = type;\r\n    console.log(this.brokerType);\r\n  }\r\n\r\n  get verificationCodeControls() {\r\n    return (this.registrationForm.get('verificationCode') as FormArray)\r\n      .controls;\r\n  }\r\n\r\n  onDigitInput(index: number): void {\r\n    const code = this.verificationDigits.join('');\r\n    this.registrationForm.patchValue({ verificationCode: code });\r\n  }\r\n\r\n  // Helper methods for validation\r\n  isFieldInvalid(fieldName: string): boolean {\r\n    const field = this.registrationForm.get(fieldName);\r\n    return !!(field && field.invalid && (field.dirty || field.touched));\r\n  }\r\n\r\n  markFieldAsTouched(fieldName: string): void {\r\n    this.registrationForm.get(fieldName)?.markAsTouched();\r\n  }\r\n\r\n  getFieldError(fieldName: string): string {\r\n    const field = this.registrationForm.get(fieldName);\r\n    if (!field?.errors) return '';\r\n\r\n    const errors = field.errors;\r\n    if (errors['required']) return 'This field is required';\r\n    if (errors['pattern'] && fieldName === 'fullName') return 'Name cannot contain numbers';\r\n    if (errors['pattern'] && fieldName === 'input') return 'Enter valid email or phone number';\r\n    if (errors['pattern'] && fieldName === 'phone') return 'Enter valid phone number';\r\n    if (errors['pattern'] && fieldName === 'password') return 'Need uppercase, lowercase & number';\r\n    if (errors['email']) return 'Enter valid email';\r\n    if (errors['minlength']) return `Min ${errors['minlength'].requiredLength} chars`;\r\n\r\n    return 'Invalid input';\r\n  }\r\n\r\n  getFormError(): string {\r\n    const password = this.registrationForm.get('password')?.value;\r\n    const confirmPassword = this.registrationForm.get('password_confirmation')?.value;\r\n\r\n    if (password && confirmPassword && password !== confirmPassword) {\r\n      return 'Passwords do not match';\r\n    }\r\n    return '';\r\n  }\r\n\r\n  // Check if step is valid\r\n  isStep1Valid(): boolean {\r\n    const fullName = this.registrationForm.get('fullName');\r\n    const input = this.registrationForm.get('input');\r\n\r\n    return !!(fullName?.valid && input?.valid);\r\n  }\r\n\r\n  isStep2Valid(): boolean {\r\n    const verificationCode = this.registrationForm.get('verificationCode') as FormArray;\r\n    return verificationCode.valid;\r\n  }\r\n\r\n  isStep7Valid(): boolean {\r\n    const phone = this.registrationForm.get('phone');\r\n    const email = this.registrationForm.get('email');\r\n    const password = this.registrationForm.get('password');\r\n    const passwordConfirmation = this.registrationForm.get('password_confirmation');\r\n    const agreeTerms = this.registrationForm.get('agreeTerms');\r\n\r\n    // Check if passwords match\r\n    const passwordsMatch = password?.value === passwordConfirmation?.value;\r\n\r\n    return !!(\r\n      phone?.valid &&\r\n      email?.valid &&\r\n      password?.valid &&\r\n      passwordConfirmation?.valid &&\r\n      agreeTerms?.valid &&\r\n      passwordsMatch\r\n    );\r\n  }\r\n\r\n  handleNextStepAndSendCode(): void {\r\n    this.sendVerificationCode(true);\r\n  }\r\n\r\n  sendVerificationCode(moveToNextStep: boolean = false) {\r\n    if (!this.isStep1Valid()) {\r\n      this.markFieldAsTouched('fullName');\r\n      this.markFieldAsTouched('input');\r\n      return;\r\n    }\r\n\r\n    this.isLoadingSendOtp = true;\r\n    this.otpErrorMessage = '';\r\n    const input = this.registrationForm.get('input')?.value?.trim();\r\n\r\n    let params: { email?: string; phone?: string } = {};\r\n    const isEmail = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(input);\r\n    const isPhone = /^[0-9+\\-\\s]{7,15}$/.test(input);\r\n\r\n    if (isEmail) {\r\n      params.email = input;\r\n    } else if (isPhone) {\r\n      params.phone = input;\r\n    }\r\n\r\n    this.authenticationService.sendOtp(params).subscribe({\r\n      next: (response: any) => {\r\n        console.log('OTP sent:', response);\r\n        this.isLoadingSendOtp = false;\r\n        this.startCountdown();\r\n        if (moveToNextStep) {\r\n          this.nextStep();\r\n        }\r\n        this.cd.markForCheck();\r\n      },\r\n      error: (error: any) => {\r\n        console.error('Failed to send OTP:', error);\r\n        this.isLoadingSendOtp = false;\r\n        this.otpErrorMessage = error.message || 'Failed to send verification code. Please try again.';\r\n        this.cd.markForCheck();\r\n      }\r\n    });\r\n  }\r\n\r\n  checkOTP() {\r\n    if (!this.isStep2Valid()) {\r\n      // Mark all verification code inputs as touched\r\n      const verificationCodeArray = this.registrationForm.get('verificationCode') as FormArray;\r\n      verificationCodeArray.controls.forEach(control => control.markAsTouched());\r\n      return;\r\n    }\r\n\r\n    this.isLoadingCheckOtp = true;\r\n    this.otpErrorMessage = '';\r\n    const input = this.registrationForm.get('input')?.value?.trim();\r\n    const codeArray = this.registrationForm.get('verificationCode')?.value;\r\n    const otp = codeArray.join('');\r\n\r\n    let params: { email?: string; phone?: string; otp?: number } = {};\r\n    const isEmail = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(input);\r\n    const isPhone = /^[0-9+\\-\\s]{7,15}$/.test(input);\r\n\r\n    if (isEmail) {\r\n      params.email = input;\r\n    } else if (isPhone) {\r\n      params.phone = input;\r\n    }\r\n\r\n    params.otp = otp;\r\n\r\n    this.authenticationService.checkOtp(params).subscribe({\r\n      next: (response: any) => {\r\n        console.log('OTP checked:', response);\r\n        this.isLoadingCheckOtp = false;\r\n        this.nextStep();\r\n        this.cd.markForCheck();\r\n      },\r\n      error: (error: any) => {\r\n        console.error('Failed to check OTP:', error);\r\n        this.isLoadingCheckOtp = false;\r\n        this.otpErrorMessage = error?.error?.message || 'Invalid verification code. Please try again.';\r\n        this.cd.markForCheck();\r\n      }\r\n    });\r\n  }\r\n\r\n  createAccount(): void {\r\n    if (!this.isStep7Valid()) {\r\n      this.markFieldAsTouched('phone');\r\n      this.markFieldAsTouched('email');\r\n      this.markFieldAsTouched('password');\r\n      this.markFieldAsTouched('password_confirmation');\r\n      this.markFieldAsTouched('agreeTerms');\r\n      return;\r\n    }\r\n\r\n    this.isLoadingCreateAccount = true;\r\n    this.createAccountErrorMessage = '';\r\n    const input = this.registrationForm.get('input')?.value?.trim();\r\n    let params = this.registrationForm.value;\r\n    params.role = 'broker';\r\n    params.type = this.brokerType;\r\n    const isEmail = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(input);\r\n    const isPhone = /^[0-9+\\-\\s]{7,15}$/.test(input);\r\n\r\n    if (isEmail) {\r\n      params.email = input;\r\n    } else if (isPhone) {\r\n      params.phone = input;\r\n    }\r\n\r\n    const specializationParams = this.getSpecializationScopesPayload();\r\n    const formData = new FormData();\r\n\r\n    // Append all form fields\r\n    for (const key in params) {\r\n      if (params.hasOwnProperty(key)) {\r\n        formData.append(key, params[key]);\r\n      }\r\n    }\r\n\r\n    if(this.selectedAreaId != 0){\r\n      params.areaIds = [this.selectedAreaId];\r\n      params.areaIds?.forEach((id: any) => formData.append('areaIds[]', id));\r\n    }\r\n\r\n    if(this.selectedSubAreaId != 0){\r\n      params.subAreas = [this.selectedSubAreaId];\r\n      params.subAreas?.forEach((id: any) => formData.append('subAreas[]', id));\r\n    }\r\n\r\n    // Append specializationScopes (array-like)\r\n    for (const key in specializationParams) {\r\n      const values = specializationParams[key];\r\n      values.forEach(value => {\r\n        formData.append(key, value);\r\n      });\r\n    }\r\n\r\n    // Append uploaded files (if any)\r\n    for (const fileType in this.uploadedFiles) {\r\n      const files = this.uploadedFiles[fileType];\r\n      if (files?.length) {\r\n        // Append all files for each field type, replacing any previous files\r\n        files.forEach((file) => {\r\n          formData.append(fileType, file);\r\n        });\r\n      }\r\n    }\r\n\r\n    this.authenticationService.register(formData).subscribe({\r\n      next: (response: any) => {\r\n        console.log('registered successfully', response);\r\n        let user = response.data;\r\n        localStorage.setItem('authToken', user.authToken);\r\n        this.authenticationService.setCurrentUser(response.data);\r\n        this.isLoadingCreateAccount = false;\r\n        this.nextStep();\r\n        this.cd.markForCheck();\r\n      },\r\n      error: (error: any) => {\r\n        console.error('Failed to register:', error);\r\n        this.isLoadingCreateAccount = false;\r\n        this.createAccountErrorMessage = error?.error?.message || 'Failed to create account. Please try again.';\r\n        this.cd.markForCheck();\r\n      }\r\n    });\r\n  }\r\n\r\n  startCountdown() {\r\n    this.showResendButton = false;\r\n    this.countdown = 25;\r\n\r\n    const intervalId = setInterval(() => {\r\n      this.countdown--;\r\n      if (this.countdown === 0) {\r\n        clearInterval(intervalId);\r\n        this.showResendButton = true;\r\n      }\r\n      this.cd.markForCheck();\r\n    }, 1000);\r\n  }\r\n\r\n  autoFocusNext(event: any, index: number): void {\r\n    const input = event.target;\r\n    if (input.value && index < 5) {\r\n      const nextInput =\r\n        input.parentElement?.nextElementSibling?.querySelector('input');\r\n      nextInput?.focus();\r\n    }\r\n  }\r\n\r\n  private clearOtpInputs() {\r\n    this.verificationDigits = ['', '', '', '', ''];\r\n    const verificationCodeArray = this.registrationForm.get(\r\n      'verificationCode'\r\n    ) as FormArray;\r\n    verificationCodeArray.controls.forEach((control) => {\r\n      control.setValue('');\r\n      control.markAsUntouched();\r\n      control.markAsPristine();\r\n    });\r\n  }\r\n\r\n  onResendCode() {\r\n    this.clearOtpInputs();\r\n    this.otpErrorMessage = '';\r\n    this.sendVerificationCode(false);\r\n  }\r\n\r\n  toSnakeCase(str: string): string {\r\n    return str\r\n      .toLowerCase()\r\n      .replace(/[^a-z0-9]+/g, '_')\r\n      .replace(/^_|_$/g, '');\r\n  }\r\n\r\n  getSpecializationScopesPayload(): { [key: string]: string[] } {\r\n    const result: { [key: string]: string[] } = {};\r\n\r\n    for (const scope of this.staticScopes) {\r\n      if (scope.selectedTypes && scope.selectedTypes.length > 0) {\r\n        const key = this.toSnakeCase(scope.specialization_scope);\r\n        result[`specializationScopes[${key}][]`] = scope.selectedTypes.map(type =>\r\n          this.toSnakeCase(type)\r\n        );\r\n      }\r\n    }\r\n\r\n    return result;\r\n  }\r\n\r\n  specializationDisplayMap: { [key: string]: string } = {\r\n    'purchasing_sell_residential_outside_compound': 'Apartments - duplexes - penthouses - roof - basements - studio',\r\n    'purchasing_sell_national_housing_projects_outside_compound': 'National Housing - Mixed Housing - Youth Housing - Ganet Masr - Dar Masr - Sakan Masr',\r\n    'purchasing_sell_administrative_commercial_units_outside_compound': 'Administrative Units - Commercial - Pharmacies - Clinics - Shops',\r\n    'purchasing_sell_industrial_and_warehousing_outside_compound': 'Factories - Warehouses - Industrial Lands - Warehouse Lands',\r\n    'purchasing_sell_lands_and_ready_projects_outside_compound': 'Administrative & Commercial Lands - Commercial Administrative Malls',\r\n    'purchasing_sell_villas_and_buildings_outside_compound': 'Villas - Full Buildings - Residential Lands - Concrete Structure',\r\n    'purchasing_sell_residential_inside_compound': 'Apartments - duplexes - penthouses - i villa - studio',\r\n    'purchasing_sell_villas_inside_compound': 'Villas - Standalone - town house - twin house',\r\n    'purchasing_sell_administrative_commercial_units_inside_compound': 'Administrative Units - Commercial - Pharmacies - Clinics - Shops',\r\n    'rent_residential_inside_compound': 'Apartments - duplexes - penthouses - i villa - villas - studio',\r\n    'rent_hotel_Units_inside_compound': 'Hotel Units',\r\n    'rent_administrative_commercial_units_inside_compound': 'Administrative Units - Commercial - Pharmacies - Clinics - Shops',\r\n    'rent_residential_outside_compound': 'Apartments - duplexes - penthouses - roof - basements - villas - studio',\r\n    'rent_national_housing_projects_compound': 'National Housing - Mixed Housing - Youth Housing - Ganet Masr - Dar Masr - Sakan Masr',\r\n    'rent_administrative_commercial_units_outside_compound': 'Administrative Units - Commercial - Pharmacies - Clinics - Shops - full buildings',\r\n    'rent_industrial_and_warehousing_outside_compound': 'Factories - Warehouses',\r\n    'rent_hotel_units_outside_compound': 'Hotel Units'\r\n  };\r\n\r\n\r\n}\r\n", "<div class=\"client-registration-stepper\">\r\n  <!-- Stepper Header -->\r\n  <div class=\"stepper-header\">\r\n    <h2 class=\"stepper-title\">Broker Registration</h2>\r\n    <div class=\"stepper-progress\">\r\n      <div class=\"progress-bar\">\r\n        <div class=\"progress-fill\" [style.width.%]=\"(currentStep / totalSteps) * 100\"></div>\r\n      </div>\r\n      <div class=\"d-flex\">\r\n        <span class=\"progress-text\">Step {{ currentStep }} of {{ totalSteps }}</span>\r\n        <button *ngIf=\"currentStep > 0 && currentStep < 8\" type=\"button\" class=\"back-to-previous\"\r\n          (click)=\"previousStep()\">\r\n          Back to previous step\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Stepper Content -->\r\n  <form [formGroup]=\"registrationForm\" class=\"stepper-form\">\r\n    <!-- Step 1: Basic Information -->\r\n    <div *ngIf=\"currentStep === 1\" class=\"step-content\">\r\n      <h3 class=\"step-title\">Enter Your Basic Information</h3>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"fullName\" class=\"form-label\">\r\n          <i class=\"ki-outline ki-user\"></i>\r\n          Full Name <span class=\"required\"></span>\r\n        </label>\r\n        <input type=\"text\" id=\"fullName\" formControlName=\"fullName\" class=\"form-control\"\r\n          [class.is-invalid]=\"isFieldInvalid('fullName')\" placeholder=\"Enter full name...\" pattern=\"[^0-9]*\"\r\n          title=\"Name cannot contain numbers\" (blur)=\"markFieldAsTouched('fullName')\" required />\r\n        <div *ngIf=\"isFieldInvalid('fullName')\" class=\"invalid-feedback\">\r\n          {{ getFieldError(\"fullName\") }}\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"input\" class=\"form-label\">\r\n          <i class=\"ki-outline ki-phone\"></i>\r\n          Enter Email or Phone Number <span class=\"required\"></span>\r\n        </label>\r\n        <input type=\"text\" id=\"input\" formControlName=\"input\" class=\"form-control\"\r\n          [class.is-invalid]=\"isFieldInvalid('input')\" placeholder=\"<EMAIL> or 01123928909\"\r\n          title=\"Enter a valid email address or phone number\" (blur)=\"markFieldAsTouched('input')\" required />\r\n        <div *ngIf=\"isFieldInvalid('input')\" class=\"invalid-feedback\">\r\n          {{ getFieldError(\"input\") }}\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Send Verification Button -->\r\n      <button type=\"button\" class=\"btn btn-primary btn-verification\" [class.loading]=\"isLoadingSendOtp\"\r\n        [disabled]=\"!isStep1Valid() || isLoadingSendOtp\" (click)=\"handleNextStepAndSendCode()\">\r\n        <span *ngIf=\"isLoadingSendOtp\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span>\r\n        {{ isLoadingSendOtp ? 'Sending...' : 'Send Verification Code' }}\r\n      </button>\r\n\r\n      <!-- Help Text -->\r\n      <div class=\"help-text\">\r\n        Need help? <span class=\"contact-link\">Contact us</span>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Step 2: Verification Code -->\r\n    <div *ngIf=\"currentStep === 2\" class=\"step-content\">\r\n      <h3 class=\"step-title\">Enter Verification Code</h3>\r\n\r\n      <!-- Verification Code Input -->\r\n      <div class=\"verification-code-section\">\r\n        <div formArrayName=\"verificationCode\" class=\"verification-inputs\">\r\n          <div class=\"code-input\" *ngFor=\"let ctrl of verificationCodeControls; let i = index\">\r\n            <input type=\"text\" maxlength=\"1\" class=\"verification-input\" [formControlName]=\"i\"\r\n              [class.is-invalid]=\"ctrl.invalid && ctrl.touched\" (input)=\"autoFocusNext($event, i)\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Countdown Timer -->\r\n      <div class=\"countdown-section\">\r\n        <span class=\"countdown-text\" *ngIf=\"!showResendButton\">\r\n          Resend in\r\n          <span class=\"countdown-timer\">\r\n            0:{{ countdown < 10 ? \"0\" + countdown : countdown }} </span>\r\n          </span>\r\n\r\n          <button *ngIf=\"showResendButton\" class=\"btn btn-link\" (click)=\"onResendCode()\">\r\n            Resend Code\r\n          </button>\r\n      </div>\r\n\r\n      <!-- OTP Error Message -->\r\n      <div *ngIf=\"otpErrorMessage\" class=\"alert alert-danger mt-3\" role=\"alert\">\r\n        {{ otpErrorMessage }}\r\n      </div>\r\n\r\n      <!-- Next Button -->\r\n      <button type=\"button\" class=\"btn btn-primary btn-verification\" [class.loading]=\"isLoadingCheckOtp\"\r\n        [disabled]=\"!isStep2Valid() || isLoadingCheckOtp\" (click)=\"checkOTP()\">\r\n        <span *ngIf=\"isLoadingCheckOtp\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span>\r\n        {{ isLoadingCheckOtp ? 'Verifying...' : 'Verified - Next' }}\r\n      </button>\r\n\r\n      <!-- Help Text -->\r\n      <div class=\"help-text\">\r\n        Need help? <span class=\"contact-link\">Contact us</span>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Step 3: Broker Type Selection -->\r\n    <div *ngIf=\"currentStep === 3\" class=\"step-content\">\r\n      <h3 class=\"step-title\">Choose Broker Type</h3>\r\n\r\n      <div class=\"broker-type-section\">\r\n        <p class=\"broker-type-description\">\r\n          Please select the type of broker registration you want to proceed with\r\n        </p>\r\n\r\n        <!-- Broker Type Options -->\r\n        <div class=\"broker-type-options\">\r\n          <!-- Independent Broker -->\r\n          <div class=\"broker-type-card\" [class.selected]=\"brokerType === 'independent'\"\r\n            (click)=\"selectBrokerType('independent')\">\r\n            <div class=\"broker-type-icon\">\r\n              <i class=\"ki-outline ki-user\"></i>\r\n            </div>\r\n            <h4 class=\"broker-type-title\">Independent Broker</h4>\r\n            <p class=\"broker-type-desc\">\r\n              Register as an individual real estate broker\r\n            </p>\r\n          </div>\r\n\r\n          <!-- Real Estate Company -->\r\n          <div class=\"broker-type-card\" [class.selected]=\"brokerType === 'real_estate_brokage_company'\"\r\n            (click)=\"selectBrokerType('real_estate_brokage_company')\">\r\n            <div class=\"broker-type-icon\">\r\n              <i class=\"ki-outline ki-office-bag\"></i>\r\n            </div>\r\n            <h4 class=\"broker-type-title\">Real Estate Company</h4>\r\n            <p class=\"broker-type-desc\">\r\n              Register as a real estate brokerage company\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Continue Button -->\r\n        <button type=\"button\" class=\"btn btn-primary btn-verification\" [disabled]=\"!brokerType\" (click)=\"nextStep()\">\r\n          Continue\r\n        </button>\r\n\r\n        <!-- Help Text -->\r\n        <div class=\"help-text\">\r\n          Need help? <span class=\"contact-link\">Contact us</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Step 4: Documents Upload -->\r\n    <div *ngIf=\"currentStep === 4\" class=\"step-content\">\r\n      <h3 class=\"step-title\">Please Upload Required Documents</h3>\r\n\r\n      <div class=\"documents-section\">\r\n        <p class=\"documents-description\">\r\n          You can upload the required documents now or skip and add them later\r\n          when you first use the required services\r\n        </p>\r\n\r\n        <!-- Document Upload Cards -->\r\n        <div class=\"upload-card-container\">\r\n          <div *ngIf=\"brokerType === 'independent'\">\r\n            <!-- Profile Photo -->\r\n            <div class=\"card mb-3 cursor-pointer\">\r\n              <label for=\"image\" class=\"card-body text-center py-2\">\r\n                <div class=\"upload-icon cursor-pointer\">\r\n                  <i class=\"fas fa-arrow-up\"></i>\r\n                </div>\r\n                <span class=\"upload-text cursor-pointer\">\r\n                  Profile Photo\r\n                  <span *ngIf=\"getFileCount('image') > 0\" class=\"badge bg-success ms-2\">\r\n                    {{ getFileCount(\"image\") }}\r\n                  </span>\r\n                </span>\r\n                <div class=\"upload-subtitle\">PNG, JPG</div>\r\n                <input type=\"file\" id=\"image\" class=\"d-none\" (change)=\"onFileChange($event, 'image')\"\r\n                  accept=\".png,.jpg,.jpeg\" />\r\n              </label>\r\n            </div>\r\n\r\n            <!-- ID Document -->\r\n            <div class=\"card mb-3 cursor-pointer\">\r\n              <label for=\"idFront\" class=\"card-body text-center py-2\">\r\n                <div class=\"upload-icon cursor-pointer\">\r\n                  <i class=\"fas fa-arrow-up\"></i>\r\n                </div>\r\n                <span class=\"upload-text cursor-pointer\">\r\n                  National ID Front\r\n                  <span *ngIf=\"getFileCount('idFront') > 0\" class=\"badge bg-success ms-2\">\r\n                    {{ getFileCount(\"idFront\") }}\r\n                  </span>\r\n                </span>\r\n                <div class=\"upload-subtitle\">PNG, JPG, PDF</div>\r\n                <input type=\"file\" id=\"idFront\" class=\"d-none\" (change)=\"onFileChange($event, 'idFront')\"\r\n                  accept=\".png,.jpg,.jpeg,.pdf\" multiple />\r\n              </label>\r\n            </div>\r\n\r\n            <!-- License Document -->\r\n            <div class=\"card mb-3 cursor-pointer\">\r\n              <label for=\"idBack\" class=\"card-body text-center py-2\">\r\n                <div class=\"upload-icon cursor-pointer\">\r\n                  <i class=\"fas fa-arrow-up\"></i>\r\n                </div>\r\n                <span class=\"upload-text cursor-pointer\">\r\n                  National ID Back\r\n                  <span *ngIf=\"getFileCount('idBack') > 0\" class=\"badge bg-success ms-2\">\r\n                    {{ getFileCount(\"idBack\") }}\r\n                  </span>\r\n                </span>\r\n                <div class=\"upload-subtitle\">PNG, JPG, PDF</div>\r\n                <input type=\"file\" id=\"idBack\" class=\"d-none\" (change)=\"onFileChange($event, 'idBack')\"\r\n                  accept=\".png,.jpg,.jpeg,.pdf\" multiple />\r\n              </label>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Company Documents   -->\r\n          <div *ngIf=\"brokerType === 'real_estate_brokage_company'\">\r\n            <!-- Company Logo -->\r\n            <div class=\"card mb-3 cursor-pointer\">\r\n              <label for=\"image\" class=\"card-body text-center py-2\">\r\n                <div class=\"upload-icon cursor-pointer\">\r\n                  <i class=\"fas fa-arrow-up\"></i>\r\n                </div>\r\n                <span class=\"upload-text cursor-pointer\">\r\n                  Company logo image for account\r\n                  <span *ngIf=\"getFileCount('image') > 0\" class=\"badge bg-success ms-2\">\r\n                    {{ getFileCount(\"image\") }}\r\n                  </span>\r\n                </span>\r\n                <div class=\"upload-subtitle\">PNG, JPG</div>\r\n                <input type=\"file\" id=\"image\" class=\"d-none\" (change)=\"onFileChange($event, 'image')\"\r\n                  accept=\".png,.jpg,.jpeg\" />\r\n              </label>\r\n            </div>\r\n\r\n            <!-- Commercial Registration -->\r\n            <div class=\"card mb-3 cursor-pointer\">\r\n              <label for=\"commercialRegistryImage\" class=\"card-body text-center py-2\">\r\n                <div class=\"upload-icon cursor-pointer\">\r\n                  <i class=\"fas fa-arrow-up\"></i>\r\n                </div>\r\n                <span class=\"upload-text cursor-pointer\">\r\n                  Commercial register photo\r\n                  <span *ngIf=\"getFileCount('commercialRegistryImage') > 0\" class=\"badge bg-success ms-2\">\r\n                    {{ getFileCount(\"commercialRegistryImage\") }}\r\n                  </span>\r\n                </span>\r\n                <div class=\"upload-subtitle\">PNG, JPG, PDF</div>\r\n                <input type=\"file\" id=\"commercialRegistryImage\" class=\"d-none\"\r\n                  (change)=\"onFileChange($event, 'commercialRegistryImage')\" accept=\".png,.jpg,.jpeg,.pdf\" multiple />\r\n              </label>\r\n            </div>\r\n\r\n            <!-- Tax Card -->\r\n            <div class=\"card mb-3 cursor-pointer\">\r\n              <label for=\"taxCardImage\" class=\"card-body text-center py-2\">\r\n                <div class=\"upload-icon cursor-pointer\">\r\n                  <i class=\"fas fa-arrow-up\"></i>\r\n                </div>\r\n                <span class=\"upload-text cursor-pointer\">\r\n                  Tax card image\r\n                  <span *ngIf=\"getFileCount('taxCardImage') > 0\" class=\"badge bg-success ms-2\">\r\n                    {{ getFileCount(\"taxCardImage\") }}\r\n                  </span>\r\n                </span>\r\n                <div class=\"upload-subtitle\">PNG, JPG, PDF</div>\r\n                <input type=\"file\" id=\"taxCardImage\" class=\"d-none\" (change)=\"onFileChange($event, 'taxCardImage')\"\r\n                  accept=\".png,.jpg,.jpeg,.pdf\" multiple />\r\n              </label>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Upload Documents Button -->\r\n        <button type=\"button\" class=\"btn btn-primary btn-verification\" (click)=\"nextStep()\">\r\n          Upload Documents\r\n        </button>\r\n\r\n        <!-- Skip Button -->\r\n        <button type=\"button\" class=\"btn btn-link skip-button\" (click)=\"nextStep()\">\r\n          Skip and return later\r\n          <i class=\"ki-outline ki-arrow-right\"></i>\r\n        </button>\r\n\r\n        <!-- Help Text -->\r\n        <div class=\"help-text\">\r\n          Need help? <span class=\"contact-link\">Contact us</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Step 5: Choose Your Areas -->\r\n    <div *ngIf=\"currentStep === 5\" class=\"step-content compact-step\">\r\n      <h3 class=\"step-title\">Choose Your Work Areas</h3>\r\n\r\n      <div class=\"areas-section\">\r\n        <!-- City Selection -->\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">City</label>\r\n          <select class=\"form-control\" [value]=\"selectedCityId\" (change)=\"onCityChange($event)\">\r\n            <option value=\"\">Select City</option>\r\n            <option *ngFor=\"let city of cities\" [value]=\"city.id\">\r\n              {{ city.name_en }}\r\n            </option>\r\n          </select>\r\n        </div>\r\n\r\n        <!-- Area Selection -->\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">Area</label>\r\n          <select class=\"form-control\" [value]=\"selectedAreaId\" (change)=\"onAreaChange($event)\"\r\n            [disabled]=\"!selectedCityId\">\r\n            <option value=\"\">Select Area</option>\r\n            <option *ngFor=\"let area of areas\" [value]=\"area.id\">\r\n              {{ area.name_en }}\r\n            </option>\r\n          </select>\r\n        </div>\r\n\r\n        <!-- subArea Selection -->\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">sub Area</label>\r\n          <select class=\"form-control\" [value]=\"selectedSubAreaId\" (change)=\"onSubAreaChange($event)\"\r\n            [disabled]=\"!selectedAreaId\">\r\n            <option value=\"\">Select Sub Area</option>\r\n            <option *ngFor=\"let subarea of subAreas\" [value]=\"subarea.id\">{{ subarea.name_en }}</option>\r\n          </select>\r\n        </div>\r\n\r\n        <!-- Continue Button -->\r\n        <button type=\"button\" class=\"btn btn-primary btn-verification\" (click)=\"nextStep()\">\r\n          Continue to Specializations\r\n        </button>\r\n\r\n        <!-- Skip Button -->\r\n        <button type=\"button\" class=\"btn btn-link skip-button\" (click)=\"nextStepSkipping()\">\r\n          Skip and return later\r\n          <i class=\"ki-outline ki-arrow-right\"></i>\r\n        </button>\r\n\r\n        <!-- Help Text -->\r\n        <div class=\"help-text\">\r\n          Need help? <span class=\"contact-link\">Contact us</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Step 6: Choose Specializations -->\r\n    <div *ngIf=\"currentStep === 6\" class=\"step-content compact-step\">\r\n      <h3 class=\"step-title\">Choose Your Specializations</h3>\r\n\r\n      <div class=\"areas-section\">\r\n        <!-- Specializations -->\r\n        <div class=\"form-group\">\r\n          <div class=\"specialization-collapse\">\r\n            <div *ngFor=\"let scope of staticScopes\" class=\"specialization-item\">\r\n              <div class=\"specialization-header\" [class.disabled]=\"isOtherScopeExpanded(scope)\">\r\n                <label class=\"scope-checkbox-label\">\r\n                  <input type=\"checkbox\" [checked]=\"scope.selected\" (change)=\"onScopeSelectionChange(scope, $event)\"\r\n                    class=\"scope-checkbox\" [disabled]=\"isOtherScopeExpanded(scope)\" />\r\n                  <span class=\"specialization-name\">{{\r\n                    scope.specialization_scope\r\n                    }}</span>\r\n                </label>\r\n                <i [class]=\"\r\n                    scope.expanded ? 'ki-outline ki-up' : 'ki-outline ki-down'\r\n                  \" class=\"collapse-icon\" (click)=\"toggleSpecializationScope(scope)\"\r\n                  [class.disabled]=\"isOtherScopeExpanded(scope)\"></i>\r\n              </div>\r\n              <div *ngIf=\"scope.expanded\" class=\"specialization-content\">\r\n                <div class=\"specialization-checkboxes\">\r\n                  <label *ngFor=\"let type of scope.specializations\" class=\"checkbox-label\">\r\n                    <input type=\"checkbox\" [checked]=\"scope.selectedTypes?.includes(type)\"\r\n                      (change)=\"onSpecializationChange(scope, type, $event)\">\r\n                    <!-- <span class=\"checkbox-text\">{{ type }}</span> -->\r\n                    <span class=\"checkbox-text\">{{ specializationDisplayMap[type] || type }}</span>\r\n                  </label>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Continue Button -->\r\n        <button type=\"button\" class=\"btn btn-primary btn-verification\" (click)=\"nextStep()\">\r\n          Continue to Account Setup\r\n        </button>\r\n\r\n        <!-- Skip Button -->\r\n        <button type=\"button\" class=\"btn btn-link skip-button\" (click)=\"nextStep()\">\r\n          Skip and return later\r\n          <i class=\"ki-outline ki-arrow-right\"></i>\r\n        </button>\r\n\r\n        <!-- Help Text -->\r\n        <div class=\"help-text\">\r\n          Need help? <span class=\"contact-link\">Contact us</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Step 7: Username and Password -->\r\n    <div *ngIf=\"currentStep === 7\" class=\"step-content\">\r\n      <h3 class=\"step-title\">Enter Your Account Details</h3>\r\n\r\n      <!-- Phone -->\r\n      <div class=\"form-group\">\r\n        <label for=\"phone\" class=\"form-label\">\r\n          <i class=\"ki-outline ki-phone\"></i>\r\n          Phone <span class=\"required\"></span>\r\n        </label>\r\n        <input type=\"tel\" id=\"phone\" formControlName=\"phone\" class=\"form-control\"\r\n          [class.is-invalid]=\"isFieldInvalid('phone')\" placeholder=\"01xxxxxxxxx\" required autocomplete=\"tel\"\r\n          (blur)=\"markFieldAsTouched('phone')\" />\r\n        <div *ngIf=\"isFieldInvalid('phone')\" class=\"invalid-feedback\">\r\n          {{ getFieldError(\"phone\") }}\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Email -->\r\n      <div class=\"form-group\">\r\n        <label for=\"email\" class=\"form-label\">\r\n          <i class=\"ki-outline ki-user\"></i>\r\n          Email\r\n        </label>\r\n        <input type=\"email\" id=\"email\" formControlName=\"email\" class=\"form-control\"\r\n          [class.is-invalid]=\"isFieldInvalid('email')\" placeholder=\"<EMAIL>...\" autocomplete=\"email\"\r\n          (blur)=\"markFieldAsTouched('email')\" />\r\n        <div *ngIf=\"isFieldInvalid('email')\" class=\"invalid-feedback\">\r\n          {{ getFieldError(\"email\") }}\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Password -->\r\n      <div class=\"form-group\">\r\n        <label for=\"password\" class=\"form-label\">\r\n          <i class=\"ki-outline ki-lock\"></i>\r\n          Password <span class=\"required\"></span>\r\n        </label>\r\n        <input type=\"password\" id=\"password\" formControlName=\"password\" class=\"form-control\"\r\n          [class.is-invalid]=\"isFieldInvalid('password')\" placeholder=\"********\" minlength=\"8\"\r\n          pattern=\"^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d).{8,}$\"\r\n          title=\"Password must be at least 8 characters with uppercase, lowercase and number\" required\r\n          autocomplete=\"new-password\" (blur)=\"markFieldAsTouched('password')\" />\r\n        <div *ngIf=\"isFieldInvalid('password')\" class=\"invalid-feedback\">\r\n          {{ getFieldError(\"password\") }}\r\n        </div>\r\n      </div>\r\n\r\n      <!--Confirm Password -->\r\n      <div class=\"form-group\">\r\n        <label for=\"confirmPassword\" class=\"form-label\">\r\n          <i class=\"ki-outline ki-lock\"></i>\r\n          Confirm Password <span class=\"required\"></span>\r\n        </label>\r\n        <input type=\"password\" id=\"confirmPassword\" formControlName=\"password_confirmation\" class=\"form-control\"\r\n          [class.is-invalid]=\"isFieldInvalid('password_confirmation') || getFormError()\" placeholder=\"********\" required\r\n          autocomplete=\"new-password\" (blur)=\"markFieldAsTouched('password_confirmation')\" />\r\n        <div *ngIf=\"isFieldInvalid('password_confirmation')\" class=\"invalid-feedback\">\r\n          {{ getFieldError(\"password_confirmation\") }}\r\n        </div>\r\n        <div *ngIf=\"getFormError()\" class=\"invalid-feedback\">\r\n          {{ getFormError() }}\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Terms Agreement -->\r\n      <div class=\"form-group\">\r\n        <div class=\"form-check\">\r\n          <input type=\"checkbox\" id=\"agreeTerms\" formControlName=\"agreeTerms\" class=\"form-check-input\"\r\n            [class.is-invalid]=\"isFieldInvalid('agreeTerms')\" />\r\n          <label for=\"agreeTerms\" class=\"form-check-label\">\r\n            I agree to the Terms and Conditions <span class=\"required\"></span>\r\n          </label>\r\n        </div>\r\n        <div *ngIf=\"isFieldInvalid('agreeTerms')\" class=\"invalid-feedback\">\r\n          {{ getFieldError(\"agreeTerms\") }}\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Create Account Error Message -->\r\n      <div *ngIf=\"createAccountErrorMessage\" class=\"alert alert-danger mt-3\" role=\"alert\">\r\n        {{ createAccountErrorMessage }}\r\n      </div>\r\n\r\n      <!-- Create Account Button -->\r\n      <button type=\"button\" class=\"btn btn-primary btn-verification\" [class.loading]=\"isLoadingCreateAccount\"\r\n        [disabled]=\"!isStep7Valid() || isLoadingCreateAccount\" (click)=\"createAccount()\">\r\n        <span *ngIf=\"isLoadingCreateAccount\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span>\r\n        {{ isLoadingCreateAccount ? 'Creating Account...' : 'Create Account' }}\r\n      </button>\r\n\r\n      <!-- Help Text -->\r\n      <div class=\"help-text\">\r\n        Need help? <span class=\"contact-link\">Contact us</span>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Step 8: Success Page -->\r\n    <div *ngIf=\"currentStep === 8\" class=\"step-content success-step\">\r\n      <div class=\"success-content\">\r\n        <div class=\"success-icon\">\r\n          <i class=\"ki-outline ki-check-circle\"></i>\r\n        </div>\r\n\r\n        <h3 class=\"success-title\">Registration Successful</h3>\r\n\r\n        <p class=\"success-message\">\r\n          Your account has been successfully created. You can now enjoy the\r\n          various and amazing services provided by Easy Deal through the website\r\n          or dashboard.\r\n        </p>\r\n\r\n        <div class=\"success-illustration\">\r\n          <!-- <img src=\"/assets/media/login/successfully.png\" alt=\"Success\" class=\"success-image\" /> -->\r\n          <!-- <img src=\"~src/assets/media/login/successfully.png\" alt=\"Success\" class=\"success-image\" /> -->\r\n          <img src=\"assets/media/login/successfully.png\" alt=\"Success\" class=\"success-image\" />\r\n        </div>\r\n\r\n        <button type=\"button\" class=\"btn btn-primary btn-success-action\" [routerLink]=\"['/broker/dashboard']\">\r\n          Go to Dashboard\r\n        </button>\r\n\r\n        <div class=\"additional-info\">\r\n          <span class=\"info-link\">Learn all about your account and how to get started</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </form>\r\n</div>"], "mappings": "AAAA,SAGEA,YAAY,QAEP,eAAe;AACtB,SAA4CC,UAAU,QAAQ,gBAAgB;;;;;;;;;;;ICItEC,EAAA,CAAAC,cAAA,iBAC2B;IAAzBD,EAAA,CAAAE,UAAA,mBAAAC,8EAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IACxBT,EAAA,CAAAU,MAAA,8BACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;;;IAmBTX,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAQ,aAAA,kBACF;;;;;IAWAd,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAQ,aAAA,eACF;;;;;IAMAd,EAAA,CAAAe,SAAA,eAAkG;;;;;;IA/BpGf,EADF,CAAAC,cAAA,cAAoD,aAC3B;IAAAD,EAAA,CAAAU,MAAA,mCAA4B;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAGtDX,EADF,CAAAC,cAAA,cAAwB,gBACmB;IACvCD,EAAA,CAAAe,SAAA,YAAkC;IAClCf,EAAA,CAAAU,MAAA,kBAAU;IAAAV,EAAA,CAAAe,SAAA,eAA8B;IAC1Cf,EAAA,CAAAW,YAAA,EAAQ;IACRX,EAAA,CAAAC,cAAA,gBAEyF;IAAnDD,EAAA,CAAAE,UAAA,kBAAAc,yEAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAQF,MAAA,CAAAY,kBAAA,CAAmB,UAAU,CAAC;IAAA,EAAC;IAF7ElB,EAAA,CAAAW,YAAA,EAEyF;IACzFX,EAAA,CAAAmB,UAAA,IAAAC,wDAAA,kBAAiE;IAGnEpB,EAAA,CAAAW,YAAA,EAAM;IAGJX,EADF,CAAAC,cAAA,eAAwB,iBACgB;IACpCD,EAAA,CAAAe,SAAA,aAAmC;IACnCf,EAAA,CAAAU,MAAA,qCAA4B;IAAAV,EAAA,CAAAe,SAAA,gBAA8B;IAC5Df,EAAA,CAAAW,YAAA,EAAQ;IACRX,EAAA,CAAAC,cAAA,iBAEsG;IAAhDD,EAAA,CAAAE,UAAA,kBAAAmB,0EAAA;MAAArB,EAAA,CAAAI,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAQF,MAAA,CAAAY,kBAAA,CAAmB,OAAO,CAAC;IAAA,EAAC;IAF1FlB,EAAA,CAAAW,YAAA,EAEsG;IACtGX,EAAA,CAAAmB,UAAA,KAAAG,yDAAA,kBAA8D;IAGhEtB,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAC,cAAA,kBACyF;IAAtCD,EAAA,CAAAE,UAAA,mBAAAqB,4EAAA;MAAAvB,EAAA,CAAAI,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAkB,yBAAA,EAA2B;IAAA,EAAC;IACtFxB,EAAA,CAAAmB,UAAA,KAAAM,0DAAA,mBAA2F;IAC3FzB,EAAA,CAAAU,MAAA,IACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAU,MAAA,oBAAW;IAAAV,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAU,MAAA,kBAAU;IAEpDV,EAFoD,CAAAW,YAAA,EAAO,EACnD,EACF;;;;IA/BAX,EAAA,CAAAY,SAAA,GAA+C;IAA/CZ,EAAA,CAAA0B,WAAA,eAAApB,MAAA,CAAAqB,cAAA,aAA+C;IAE3C3B,EAAA,CAAAY,SAAA,EAAgC;IAAhCZ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAqB,cAAA,aAAgC;IAWpC3B,EAAA,CAAAY,SAAA,GAA4C;IAA5CZ,EAAA,CAAA0B,WAAA,eAAApB,MAAA,CAAAqB,cAAA,UAA4C;IAExC3B,EAAA,CAAAY,SAAA,EAA6B;IAA7BZ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAqB,cAAA,UAA6B;IAM0B3B,EAAA,CAAAY,SAAA,EAAkC;IAAlCZ,EAAA,CAAA0B,WAAA,YAAApB,MAAA,CAAAuB,gBAAA,CAAkC;IAC/F7B,EAAA,CAAA4B,UAAA,cAAAtB,MAAA,CAAAwB,YAAA,MAAAxB,MAAA,CAAAuB,gBAAA,CAAgD;IACzC7B,EAAA,CAAAY,SAAA,EAAsB;IAAtBZ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAuB,gBAAA,CAAsB;IAC7B7B,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAuB,gBAAA,gDACF;;;;;;IAgBM7B,EADF,CAAAC,cAAA,cAAqF,gBAEM;IAArCD,EAAA,CAAAE,UAAA,mBAAA6B,gFAAAC,MAAA;MAAA,MAAAC,IAAA,GAAAjC,EAAA,CAAAI,aAAA,CAAA8B,GAAA,EAAAC,KAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA8B,aAAA,CAAAJ,MAAA,EAAAC,IAAA,CAAwB;IAAA,EAAC;IACxFjC,EAFE,CAAAW,YAAA,EACyF,EACrF;;;;;IADFX,EAAA,CAAAY,SAAA,EAAiD;IAAjDZ,EAAA,CAAA0B,WAAA,eAAAW,OAAA,CAAAC,OAAA,IAAAD,OAAA,CAAAE,OAAA,CAAiD;IADSvC,EAAA,CAAA4B,UAAA,oBAAAK,IAAA,CAAqB;;;;;IAQrFjC,EAAA,CAAAC,cAAA,eAAuD;IACrDD,EAAA,CAAAU,MAAA,kBACA;IAAAV,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAU,MAAA,GAAqD;IACvDV,EADuD,CAAAW,YAAA,EAAO,EACvD;;;;IADLX,EAAA,CAAAY,SAAA,GAAqD;IAArDZ,EAAA,CAAAa,kBAAA,QAAAP,MAAA,CAAAkC,SAAA,cAAAlC,MAAA,CAAAkC,SAAA,GAAAlC,MAAA,CAAAkC,SAAA,MAAqD;;;;;;IAGvDxC,EAAA,CAAAC,cAAA,iBAA+E;IAAzBD,EAAA,CAAAE,UAAA,mBAAAuC,oFAAA;MAAAzC,EAAA,CAAAI,aAAA,CAAAsC,GAAA;MAAA,MAAApC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqC,YAAA,EAAc;IAAA,EAAC;IAC5E3C,EAAA,CAAAU,MAAA,oBACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;;;IAIbX,EAAA,CAAAC,cAAA,cAA0E;IACxED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAsC,eAAA,MACF;;;;;IAKE5C,EAAA,CAAAe,SAAA,eAAmG;;;;;;IAjCrGf,EADF,CAAAC,cAAA,cAAoD,aAC3B;IAAAD,EAAA,CAAAU,MAAA,8BAAuB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAIjDX,EADF,CAAAC,cAAA,cAAuC,cAC6B;IAChED,EAAA,CAAAmB,UAAA,IAAA0B,wDAAA,kBAAqF;IAKzF7C,EADE,CAAAW,YAAA,EAAM,EACF;IAGNX,EAAA,CAAAC,cAAA,cAA+B;IAO3BD,EANF,CAAAmB,UAAA,IAAA2B,yDAAA,mBAAuD,IAAAC,2DAAA,qBAM0B;IAGnF/C,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAmB,UAAA,IAAA6B,wDAAA,kBAA0E;IAK1EhD,EAAA,CAAAC,cAAA,kBACyE;IAArBD,EAAA,CAAAE,UAAA,mBAAA+C,4EAAA;MAAAjD,EAAA,CAAAI,aAAA,CAAA8C,GAAA;MAAA,MAAA5C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA6C,QAAA,EAAU;IAAA,EAAC;IACtEnD,EAAA,CAAAmB,UAAA,KAAAiC,0DAAA,mBAA4F;IAC5FpD,EAAA,CAAAU,MAAA,IACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAU,MAAA,oBAAW;IAAAV,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAU,MAAA,kBAAU;IAEpDV,EAFoD,CAAAW,YAAA,EAAO,EACnD,EACF;;;;IApCyCX,EAAA,CAAAY,SAAA,GAA6B;IAA7BZ,EAAA,CAAA4B,UAAA,YAAAtB,MAAA,CAAA+C,wBAAA,CAA6B;IAS1CrD,EAAA,CAAAY,SAAA,GAAuB;IAAvBZ,EAAA,CAAA4B,UAAA,UAAAtB,MAAA,CAAAgD,gBAAA,CAAuB;IAM1CtD,EAAA,CAAAY,SAAA,EAAsB;IAAtBZ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAgD,gBAAA,CAAsB;IAM7BtD,EAAA,CAAAY,SAAA,EAAqB;IAArBZ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAsC,eAAA,CAAqB;IAKoC5C,EAAA,CAAAY,SAAA,EAAmC;IAAnCZ,EAAA,CAAA0B,WAAA,YAAApB,MAAA,CAAAiD,iBAAA,CAAmC;IAChGvD,EAAA,CAAA4B,UAAA,cAAAtB,MAAA,CAAAkD,YAAA,MAAAlD,MAAA,CAAAiD,iBAAA,CAAiD;IAC1CvD,EAAA,CAAAY,SAAA,EAAuB;IAAvBZ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAiD,iBAAA,CAAuB;IAC9BvD,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAiD,iBAAA,2CACF;;;;;;IAUAvD,EADF,CAAAC,cAAA,cAAoD,aAC3B;IAAAD,EAAA,CAAAU,MAAA,yBAAkB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAG5CX,EADF,CAAAC,cAAA,cAAiC,YACI;IACjCD,EAAA,CAAAU,MAAA,+EACF;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAKFX,EAFF,CAAAC,cAAA,cAAiC,cAGa;IAA1CD,EAAA,CAAAE,UAAA,mBAAAuD,wEAAA;MAAAzD,EAAA,CAAAI,aAAA,CAAAsD,GAAA;MAAA,MAAApD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqD,gBAAA,CAAiB,aAAa,CAAC;IAAA,EAAC;IACzC3D,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAe,SAAA,YAAkC;IACpCf,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAU,MAAA,0BAAkB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACrDX,EAAA,CAAAC,cAAA,aAA4B;IAC1BD,EAAA,CAAAU,MAAA,sDACF;IACFV,EADE,CAAAW,YAAA,EAAI,EACA;IAGNX,EAAA,CAAAC,cAAA,eAC4D;IAA1DD,EAAA,CAAAE,UAAA,mBAAA0D,yEAAA;MAAA5D,EAAA,CAAAI,aAAA,CAAAsD,GAAA;MAAA,MAAApD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqD,gBAAA,CAAiB,6BAA6B,CAAC;IAAA,EAAC;IACzD3D,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAe,SAAA,aAAwC;IAC1Cf,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAU,MAAA,2BAAmB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACtDX,EAAA,CAAAC,cAAA,aAA4B;IAC1BD,EAAA,CAAAU,MAAA,qDACF;IAEJV,EAFI,CAAAW,YAAA,EAAI,EACA,EACF;IAGNX,EAAA,CAAAC,cAAA,kBAA6G;IAArBD,EAAA,CAAAE,UAAA,mBAAA2D,4EAAA;MAAA7D,EAAA,CAAAI,aAAA,CAAAsD,GAAA;MAAA,MAAApD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAwD,QAAA,EAAU;IAAA,EAAC;IAC1G9D,EAAA,CAAAU,MAAA,kBACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAU,MAAA,oBAAW;IAAAV,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAU,MAAA,kBAAU;IAGtDV,EAHsD,CAAAW,YAAA,EAAO,EACnD,EACF,EACF;;;;IAlC8BX,EAAA,CAAAY,SAAA,GAA+C;IAA/CZ,EAAA,CAAA0B,WAAA,aAAApB,MAAA,CAAAyD,UAAA,mBAA+C;IAY/C/D,EAAA,CAAAY,SAAA,GAA+D;IAA/DZ,EAAA,CAAA0B,WAAA,aAAApB,MAAA,CAAAyD,UAAA,mCAA+D;IAahC/D,EAAA,CAAAY,SAAA,GAAwB;IAAxBZ,EAAA,CAAA4B,UAAA,cAAAtB,MAAA,CAAAyD,UAAA,CAAwB;;;;;IAgC7E/D,EAAA,CAAAC,cAAA,eAAsE;IACpED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IADLX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAA0D,YAAA,eACF;;;;;IAgBAhE,EAAA,CAAAC,cAAA,eAAwE;IACtED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IADLX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAA0D,YAAA,iBACF;;;;;IAgBAhE,EAAA,CAAAC,cAAA,eAAuE;IACrED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IADLX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAA0D,YAAA,gBACF;;;;;;IA3CFhE,EAJN,CAAAC,cAAA,UAA0C,cAEF,gBACkB,cACZ;IACtCD,EAAA,CAAAe,SAAA,YAA+B;IACjCf,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,eAAyC;IACvCD,EAAA,CAAAU,MAAA,sBACA;IAAAV,EAAA,CAAAmB,UAAA,IAAA8C,+DAAA,mBAAsE;IAGxEjE,EAAA,CAAAW,YAAA,EAAO;IACPX,EAAA,CAAAC,cAAA,cAA6B;IAAAD,EAAA,CAAAU,MAAA,eAAQ;IAAAV,EAAA,CAAAW,YAAA,EAAM;IAC3CX,EAAA,CAAAC,cAAA,iBAC6B;IADgBD,EAAA,CAAAE,UAAA,oBAAAgE,kFAAAlC,MAAA;MAAAhC,EAAA,CAAAI,aAAA,CAAA+D,IAAA;MAAA,MAAA7D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAUF,MAAA,CAAA8D,YAAA,CAAApC,MAAA,EAAqB,OAAO,CAAC;IAAA,EAAC;IAGzFhC,EAHI,CAAAW,YAAA,EAC6B,EACvB,EACJ;IAKFX,EAFJ,CAAAC,cAAA,eAAsC,iBACoB,eACd;IACtCD,EAAA,CAAAe,SAAA,aAA+B;IACjCf,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,gBAAyC;IACvCD,EAAA,CAAAU,MAAA,2BACA;IAAAV,EAAA,CAAAmB,UAAA,KAAAkD,gEAAA,mBAAwE;IAG1ErE,EAAA,CAAAW,YAAA,EAAO;IACPX,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAU,MAAA,qBAAa;IAAAV,EAAA,CAAAW,YAAA,EAAM;IAChDX,EAAA,CAAAC,cAAA,iBAC2C;IADID,EAAA,CAAAE,UAAA,oBAAAoE,kFAAAtC,MAAA;MAAAhC,EAAA,CAAAI,aAAA,CAAA+D,IAAA;MAAA,MAAA7D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAUF,MAAA,CAAA8D,YAAA,CAAApC,MAAA,EAAqB,SAAS,CAAC;IAAA,EAAC;IAG7FhC,EAHI,CAAAW,YAAA,EAC2C,EACrC,EACJ;IAKFX,EAFJ,CAAAC,cAAA,eAAsC,iBACmB,eACb;IACtCD,EAAA,CAAAe,SAAA,aAA+B;IACjCf,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,gBAAyC;IACvCD,EAAA,CAAAU,MAAA,0BACA;IAAAV,EAAA,CAAAmB,UAAA,KAAAoD,gEAAA,mBAAuE;IAGzEvE,EAAA,CAAAW,YAAA,EAAO;IACPX,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAU,MAAA,qBAAa;IAAAV,EAAA,CAAAW,YAAA,EAAM;IAChDX,EAAA,CAAAC,cAAA,iBAC2C;IADGD,EAAA,CAAAE,UAAA,oBAAAsE,kFAAAxC,MAAA;MAAAhC,EAAA,CAAAI,aAAA,CAAA+D,IAAA;MAAA,MAAA7D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAUF,MAAA,CAAA8D,YAAA,CAAApC,MAAA,EAAqB,QAAQ,CAAC;IAAA,EAAC;IAI7FhC,EAJM,CAAAW,YAAA,EAC2C,EACrC,EACJ,EACF;;;;IA7CSX,EAAA,CAAAY,SAAA,GAA+B;IAA/BZ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAA0D,YAAA,cAA+B;IAkB/BhE,EAAA,CAAAY,SAAA,IAAiC;IAAjCZ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAA0D,YAAA,gBAAiC;IAkBjChE,EAAA,CAAAY,SAAA,IAAgC;IAAhCZ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAA0D,YAAA,eAAgC;;;;;IAqBvChE,EAAA,CAAAC,cAAA,eAAsE;IACpED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IADLX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAA0D,YAAA,eACF;;;;;IAgBAhE,EAAA,CAAAC,cAAA,eAAwF;IACtFD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IADLX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAA0D,YAAA,iCACF;;;;;IAgBAhE,EAAA,CAAAC,cAAA,eAA6E;IAC3ED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IADLX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAA0D,YAAA,sBACF;;;;;;IA3CFhE,EAJN,CAAAC,cAAA,UAA0D,cAElB,gBACkB,cACZ;IACtCD,EAAA,CAAAe,SAAA,YAA+B;IACjCf,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,eAAyC;IACvCD,EAAA,CAAAU,MAAA,uCACA;IAAAV,EAAA,CAAAmB,UAAA,IAAAsD,+DAAA,mBAAsE;IAGxEzE,EAAA,CAAAW,YAAA,EAAO;IACPX,EAAA,CAAAC,cAAA,cAA6B;IAAAD,EAAA,CAAAU,MAAA,eAAQ;IAAAV,EAAA,CAAAW,YAAA,EAAM;IAC3CX,EAAA,CAAAC,cAAA,iBAC6B;IADgBD,EAAA,CAAAE,UAAA,oBAAAwE,kFAAA1C,MAAA;MAAAhC,EAAA,CAAAI,aAAA,CAAAuE,IAAA;MAAA,MAAArE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAUF,MAAA,CAAA8D,YAAA,CAAApC,MAAA,EAAqB,OAAO,CAAC;IAAA,EAAC;IAGzFhC,EAHI,CAAAW,YAAA,EAC6B,EACvB,EACJ;IAKFX,EAFJ,CAAAC,cAAA,eAAsC,iBACoC,eAC9B;IACtCD,EAAA,CAAAe,SAAA,aAA+B;IACjCf,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,gBAAyC;IACvCD,EAAA,CAAAU,MAAA,mCACA;IAAAV,EAAA,CAAAmB,UAAA,KAAAyD,gEAAA,mBAAwF;IAG1F5E,EAAA,CAAAW,YAAA,EAAO;IACPX,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAU,MAAA,qBAAa;IAAAV,EAAA,CAAAW,YAAA,EAAM;IAChDX,EAAA,CAAAC,cAAA,iBACsG;IAApGD,EAAA,CAAAE,UAAA,oBAAA2E,kFAAA7C,MAAA;MAAAhC,EAAA,CAAAI,aAAA,CAAAuE,IAAA;MAAA,MAAArE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAUF,MAAA,CAAA8D,YAAA,CAAApC,MAAA,EAAqB,yBAAyB,CAAC;IAAA,EAAC;IAEhEhC,EAHI,CAAAW,YAAA,EACsG,EAChG,EACJ;IAKFX,EAFJ,CAAAC,cAAA,eAAsC,iBACyB,eACnB;IACtCD,EAAA,CAAAe,SAAA,aAA+B;IACjCf,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,gBAAyC;IACvCD,EAAA,CAAAU,MAAA,wBACA;IAAAV,EAAA,CAAAmB,UAAA,KAAA2D,gEAAA,mBAA6E;IAG/E9E,EAAA,CAAAW,YAAA,EAAO;IACPX,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAU,MAAA,qBAAa;IAAAV,EAAA,CAAAW,YAAA,EAAM;IAChDX,EAAA,CAAAC,cAAA,iBAC2C;IADSD,EAAA,CAAAE,UAAA,oBAAA6E,kFAAA/C,MAAA;MAAAhC,EAAA,CAAAI,aAAA,CAAAuE,IAAA;MAAA,MAAArE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAUF,MAAA,CAAA8D,YAAA,CAAApC,MAAA,EAAqB,cAAc,CAAC;IAAA,EAAC;IAIzGhC,EAJM,CAAAW,YAAA,EAC2C,EACrC,EACJ,EACF;;;;IA7CSX,EAAA,CAAAY,SAAA,GAA+B;IAA/BZ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAA0D,YAAA,cAA+B;IAkB/BhE,EAAA,CAAAY,SAAA,IAAiD;IAAjDZ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAA0D,YAAA,gCAAiD;IAkBjDhE,EAAA,CAAAY,SAAA,IAAsC;IAAtCZ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAA0D,YAAA,qBAAsC;;;;;;IAhHzDhE,EADF,CAAAC,cAAA,cAAoD,aAC3B;IAAAD,EAAA,CAAAU,MAAA,uCAAgC;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAG1DX,EADF,CAAAC,cAAA,cAA+B,YACI;IAC/BD,EAAA,CAAAU,MAAA,sHAEF;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAGJX,EAAA,CAAAC,cAAA,cAAmC;IA0DjCD,EAzDA,CAAAmB,UAAA,IAAA6D,wDAAA,mBAA0C,IAAAC,wDAAA,mBAyDgB;IAuD5DjF,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAC,cAAA,iBAAoF;IAArBD,EAAA,CAAAE,UAAA,mBAAAgF,2EAAA;MAAAlF,EAAA,CAAAI,aAAA,CAAA+E,IAAA;MAAA,MAAA7E,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAwD,QAAA,EAAU;IAAA,EAAC;IACjF9D,EAAA,CAAAU,MAAA,0BACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAAC,cAAA,kBAA4E;IAArBD,EAAA,CAAAE,UAAA,mBAAAkF,4EAAA;MAAApF,EAAA,CAAAI,aAAA,CAAA+E,IAAA;MAAA,MAAA7E,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAwD,QAAA,EAAU;IAAA,EAAC;IACzE9D,EAAA,CAAAU,MAAA,+BACA;IAAAV,EAAA,CAAAe,SAAA,aAAyC;IAC3Cf,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAU,MAAA,oBAAW;IAAAV,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAU,MAAA,kBAAU;IAGtDV,EAHsD,CAAAW,YAAA,EAAO,EACnD,EACF,EACF;;;;IAlIMX,EAAA,CAAAY,SAAA,GAAkC;IAAlCZ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAyD,UAAA,mBAAkC;IAyDlC/D,EAAA,CAAAY,SAAA,EAAkD;IAAlDZ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAyD,UAAA,mCAAkD;;;;;IAqFtD/D,EAAA,CAAAC,cAAA,iBAAsD;IACpDD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;;IAF2BX,EAAA,CAAA4B,UAAA,UAAAyD,QAAA,CAAAC,EAAA,CAAiB;IACnDtF,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAwE,QAAA,CAAAE,OAAA,MACF;;;;;IAUAvF,EAAA,CAAAC,cAAA,iBAAqD;IACnDD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;;IAF0BX,EAAA,CAAA4B,UAAA,UAAA4D,QAAA,CAAAF,EAAA,CAAiB;IAClDtF,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAA2E,QAAA,CAAAD,OAAA,MACF;;;;;IAUAvF,EAAA,CAAAC,cAAA,iBAA8D;IAAAD,EAAA,CAAAU,MAAA,GAAqB;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;;IAAnDX,EAAA,CAAA4B,UAAA,UAAA6D,WAAA,CAAAH,EAAA,CAAoB;IAACtF,EAAA,CAAAY,SAAA,EAAqB;IAArBZ,EAAA,CAAA0F,iBAAA,CAAAD,WAAA,CAAAF,OAAA,CAAqB;;;;;;IAhCzFvF,EADF,CAAAC,cAAA,cAAiE,aACxC;IAAAD,EAAA,CAAAU,MAAA,6BAAsB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAK9CX,EAHJ,CAAAC,cAAA,cAA2B,cAED,gBACI;IAAAD,EAAA,CAAAU,MAAA,WAAI;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IACtCX,EAAA,CAAAC,cAAA,iBAAsF;IAAhCD,EAAA,CAAAE,UAAA,oBAAAyF,4EAAA3D,MAAA;MAAAhC,EAAA,CAAAI,aAAA,CAAAwF,IAAA;MAAA,MAAAtF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAUF,MAAA,CAAAuF,YAAA,CAAA7D,MAAA,CAAoB;IAAA,EAAC;IACnFhC,EAAA,CAAAC,cAAA,iBAAiB;IAAAD,EAAA,CAAAU,MAAA,kBAAW;IAAAV,EAAA,CAAAW,YAAA,EAAS;IACrCX,EAAA,CAAAmB,UAAA,KAAA2E,4DAAA,qBAAsD;IAI1D9F,EADE,CAAAW,YAAA,EAAS,EACL;IAIJX,EADF,CAAAC,cAAA,eAAwB,iBACI;IAAAD,EAAA,CAAAU,MAAA,YAAI;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IACtCX,EAAA,CAAAC,cAAA,kBAC+B;IADuBD,EAAA,CAAAE,UAAA,oBAAA6F,6EAAA/D,MAAA;MAAAhC,EAAA,CAAAI,aAAA,CAAAwF,IAAA;MAAA,MAAAtF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAUF,MAAA,CAAA0F,YAAA,CAAAhE,MAAA,CAAoB;IAAA,EAAC;IAEnFhC,EAAA,CAAAC,cAAA,kBAAiB;IAAAD,EAAA,CAAAU,MAAA,mBAAW;IAAAV,EAAA,CAAAW,YAAA,EAAS;IACrCX,EAAA,CAAAmB,UAAA,KAAA8E,4DAAA,qBAAqD;IAIzDjG,EADE,CAAAW,YAAA,EAAS,EACL;IAIJX,EADF,CAAAC,cAAA,eAAwB,iBACI;IAAAD,EAAA,CAAAU,MAAA,gBAAQ;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IAC1CX,EAAA,CAAAC,cAAA,kBAC+B;IAD0BD,EAAA,CAAAE,UAAA,oBAAAgG,6EAAAlE,MAAA;MAAAhC,EAAA,CAAAI,aAAA,CAAAwF,IAAA;MAAA,MAAAtF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAUF,MAAA,CAAA6F,eAAA,CAAAnE,MAAA,CAAuB;IAAA,EAAC;IAEzFhC,EAAA,CAAAC,cAAA,kBAAiB;IAAAD,EAAA,CAAAU,MAAA,uBAAe;IAAAV,EAAA,CAAAW,YAAA,EAAS;IACzCX,EAAA,CAAAmB,UAAA,KAAAiF,4DAAA,qBAA8D;IAElEpG,EADE,CAAAW,YAAA,EAAS,EACL;IAGNX,EAAA,CAAAC,cAAA,kBAAoF;IAArBD,EAAA,CAAAE,UAAA,mBAAAmG,4EAAA;MAAArG,EAAA,CAAAI,aAAA,CAAAwF,IAAA;MAAA,MAAAtF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAwD,QAAA,EAAU;IAAA,EAAC;IACjF9D,EAAA,CAAAU,MAAA,qCACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAAC,cAAA,kBAAoF;IAA7BD,EAAA,CAAAE,UAAA,mBAAAoG,4EAAA;MAAAtG,EAAA,CAAAI,aAAA,CAAAwF,IAAA;MAAA,MAAAtF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiG,gBAAA,EAAkB;IAAA,EAAC;IACjFvG,EAAA,CAAAU,MAAA,+BACA;IAAAV,EAAA,CAAAe,SAAA,aAAyC;IAC3Cf,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAU,MAAA,oBAAW;IAAAV,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAU,MAAA,kBAAU;IAGtDV,EAHsD,CAAAW,YAAA,EAAO,EACnD,EACF,EACF;;;;IA9C6BX,EAAA,CAAAY,SAAA,GAAwB;IAAxBZ,EAAA,CAAA4B,UAAA,UAAAtB,MAAA,CAAAkG,cAAA,CAAwB;IAE1BxG,EAAA,CAAAY,SAAA,GAAS;IAATZ,EAAA,CAAA4B,UAAA,YAAAtB,MAAA,CAAAmG,MAAA,CAAS;IASPzG,EAAA,CAAAY,SAAA,GAAwB;IACnDZ,EAD2B,CAAA4B,UAAA,UAAAtB,MAAA,CAAAoG,cAAA,CAAwB,cAAApG,MAAA,CAAAkG,cAAA,CACvB;IAEHxG,EAAA,CAAAY,SAAA,GAAQ;IAARZ,EAAA,CAAA4B,UAAA,YAAAtB,MAAA,CAAAqG,KAAA,CAAQ;IASN3G,EAAA,CAAAY,SAAA,GAA2B;IACtDZ,EAD2B,CAAA4B,UAAA,UAAAtB,MAAA,CAAAsG,iBAAA,CAA2B,cAAAtG,MAAA,CAAAoG,cAAA,CAC1B;IAEA1G,EAAA,CAAAY,SAAA,GAAW;IAAXZ,EAAA,CAAA4B,UAAA,YAAAtB,MAAA,CAAAuG,QAAA,CAAW;;;;;;IA+C/B7G,EADF,CAAAC,cAAA,gBAAyE,gBAEd;IAAvDD,EAAA,CAAAE,UAAA,oBAAA4G,+FAAA9E,MAAA;MAAA,MAAA+E,QAAA,GAAA/G,EAAA,CAAAI,aAAA,CAAA4G,IAAA,EAAAC,SAAA;MAAA,MAAAC,SAAA,GAAAlH,EAAA,CAAAO,aAAA,IAAA0G,SAAA;MAAA,MAAA3G,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAUF,MAAA,CAAA6G,sBAAA,CAAAD,SAAA,EAAAH,QAAA,EAAA/E,MAAA,CAA2C;IAAA,EAAC;IADxDhC,EAAA,CAAAW,YAAA,EACyD;IAEzDX,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAU,MAAA,GAA4C;IAC1EV,EAD0E,CAAAW,YAAA,EAAO,EACzE;;;;;;IAJiBX,EAAA,CAAAY,SAAA,EAA+C;IAA/CZ,EAAA,CAAA4B,UAAA,YAAAsF,SAAA,CAAAE,aAAA,kBAAAF,SAAA,CAAAE,aAAA,CAAAC,QAAA,CAAAN,QAAA,EAA+C;IAG1C/G,EAAA,CAAAY,SAAA,GAA4C;IAA5CZ,EAAA,CAAA0F,iBAAA,CAAApF,MAAA,CAAAgH,wBAAA,CAAAP,QAAA,KAAAA,QAAA,CAA4C;;;;;IAL5E/G,EADF,CAAAC,cAAA,cAA2D,cAClB;IACrCD,EAAA,CAAAmB,UAAA,IAAAoG,sEAAA,oBAAyE;IAO7EvH,EADE,CAAAW,YAAA,EAAM,EACF;;;;IAPsBX,EAAA,CAAAY,SAAA,GAAwB;IAAxBZ,EAAA,CAAA4B,UAAA,YAAAsF,SAAA,CAAAM,eAAA,CAAwB;;;;;;IAbhDxH,EAHN,CAAAC,cAAA,cAAoE,cACgB,gBAC5C,gBAEkC;IADlBD,EAAA,CAAAE,UAAA,oBAAAuH,iFAAAzF,MAAA;MAAA,MAAAkF,SAAA,GAAAlH,EAAA,CAAAI,aAAA,CAAAsH,IAAA,EAAAT,SAAA;MAAA,MAAA3G,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAUF,MAAA,CAAAqH,sBAAA,CAAAT,SAAA,EAAAlF,MAAA,CAAqC;IAAA,EAAC;IAAlGhC,EAAA,CAAAW,YAAA,EACoE;IACpEX,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAU,MAAA,GAE9B;IACNV,EADM,CAAAW,YAAA,EAAO,EACL;IACRX,EAAA,CAAAC,cAAA,YAGiD;IADvBD,EAAA,CAAAE,UAAA,mBAAA0H,4EAAA;MAAA,MAAAV,SAAA,GAAAlH,EAAA,CAAAI,aAAA,CAAAsH,IAAA,EAAAT,SAAA;MAAA,MAAA3G,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAuH,yBAAA,CAAAX,SAAA,CAAgC;IAAA,EAAC;IAEtElH,EADmD,CAAAW,YAAA,EAAI,EACjD;IACNX,EAAA,CAAAmB,UAAA,IAAA2G,8DAAA,kBAA2D;IAU7D9H,EAAA,CAAAW,YAAA,EAAM;;;;;IAvB+BX,EAAA,CAAAY,SAAA,EAA8C;IAA9CZ,EAAA,CAAA0B,WAAA,aAAApB,MAAA,CAAAyH,oBAAA,CAAAb,SAAA,EAA8C;IAEtDlH,EAAA,CAAAY,SAAA,GAA0B;IACxBZ,EADF,CAAA4B,UAAA,YAAAsF,SAAA,CAAAc,QAAA,CAA0B,aAAA1H,MAAA,CAAAyH,oBAAA,CAAAb,SAAA,EACgB;IAC/BlH,EAAA,CAAAY,SAAA,GAE9B;IAF8BZ,EAAA,CAAA0F,iBAAA,CAAAwB,SAAA,CAAAe,oBAAA,CAE9B;IAEHjI,EAAA,CAAAY,SAAA,EAEA;IAFAZ,EAAA,CAAAkI,UAAA,CAAAhB,SAAA,CAAAiB,QAAA,6CAEA;IACDnI,EAAA,CAAA0B,WAAA,aAAApB,MAAA,CAAAyH,oBAAA,CAAAb,SAAA,EAA8C;IAE5ClH,EAAA,CAAAY,SAAA,EAAoB;IAApBZ,EAAA,CAAA4B,UAAA,SAAAsF,SAAA,CAAAiB,QAAA,CAAoB;;;;;;IApBlCnI,EADF,CAAAC,cAAA,cAAiE,aACxC;IAAAD,EAAA,CAAAU,MAAA,kCAA2B;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAKnDX,EAHJ,CAAAC,cAAA,cAA2B,cAED,cACe;IACnCD,EAAA,CAAAmB,UAAA,IAAAiH,wDAAA,mBAAoE;IA0BxEpI,EADE,CAAAW,YAAA,EAAM,EACF;IAGNX,EAAA,CAAAC,cAAA,iBAAoF;IAArBD,EAAA,CAAAE,UAAA,mBAAAmI,2EAAA;MAAArI,EAAA,CAAAI,aAAA,CAAAkI,IAAA;MAAA,MAAAhI,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAwD,QAAA,EAAU;IAAA,EAAC;IACjF9D,EAAA,CAAAU,MAAA,kCACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAAC,cAAA,iBAA4E;IAArBD,EAAA,CAAAE,UAAA,mBAAAqI,2EAAA;MAAAvI,EAAA,CAAAI,aAAA,CAAAkI,IAAA;MAAA,MAAAhI,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAwD,QAAA,EAAU;IAAA,EAAC;IACzE9D,EAAA,CAAAU,MAAA,+BACA;IAAAV,EAAA,CAAAe,SAAA,aAAyC;IAC3Cf,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAU,MAAA,oBAAW;IAAAV,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAU,MAAA,kBAAU;IAGtDV,EAHsD,CAAAW,YAAA,EAAO,EACnD,EACF,EACF;;;;IA5CyBX,EAAA,CAAAY,SAAA,GAAe;IAAfZ,EAAA,CAAA4B,UAAA,YAAAtB,MAAA,CAAAkI,YAAA,CAAe;;;;;IA2D1CxI,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAQ,aAAA,eACF;;;;;IAYAd,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAQ,aAAA,eACF;;;;;IAcAd,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAQ,aAAA,kBACF;;;;;IAYAd,EAAA,CAAAC,cAAA,cAA8E;IAC5ED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAQ,aAAA,+BACF;;;;;IACAd,EAAA,CAAAC,cAAA,cAAqD;IACnDD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAmI,YAAA,QACF;;;;;IAYAzI,EAAA,CAAAC,cAAA,cAAmE;IACjED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAQ,aAAA,oBACF;;;;;IAIFd,EAAA,CAAAC,cAAA,cAAoF;IAClFD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAoI,yBAAA,MACF;;;;;IAKE1I,EAAA,CAAAe,SAAA,eAAwG;;;;;;IArF1Gf,EADF,CAAAC,cAAA,cAAoD,aAC3B;IAAAD,EAAA,CAAAU,MAAA,iCAA0B;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAIpDX,EADF,CAAAC,cAAA,cAAwB,gBACgB;IACpCD,EAAA,CAAAe,SAAA,YAAmC;IACnCf,EAAA,CAAAU,MAAA,cAAM;IAAAV,EAAA,CAAAe,SAAA,eAA8B;IACtCf,EAAA,CAAAW,YAAA,EAAQ;IACRX,EAAA,CAAAC,cAAA,iBAEyC;IAAvCD,EAAA,CAAAE,UAAA,kBAAAyI,yEAAA;MAAA3I,EAAA,CAAAI,aAAA,CAAAwI,IAAA;MAAA,MAAAtI,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAQF,MAAA,CAAAY,kBAAA,CAAmB,OAAO,CAAC;IAAA,EAAC;IAFtClB,EAAA,CAAAW,YAAA,EAEyC;IACzCX,EAAA,CAAAmB,UAAA,IAAA0H,wDAAA,kBAA8D;IAGhE7I,EAAA,CAAAW,YAAA,EAAM;IAIJX,EADF,CAAAC,cAAA,eAAwB,kBACgB;IACpCD,EAAA,CAAAe,SAAA,aAAkC;IAClCf,EAAA,CAAAU,MAAA,eACF;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IACRX,EAAA,CAAAC,cAAA,kBAEyC;IAAvCD,EAAA,CAAAE,UAAA,kBAAA4I,0EAAA;MAAA9I,EAAA,CAAAI,aAAA,CAAAwI,IAAA;MAAA,MAAAtI,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAQF,MAAA,CAAAY,kBAAA,CAAmB,OAAO,CAAC;IAAA,EAAC;IAFtClB,EAAA,CAAAW,YAAA,EAEyC;IACzCX,EAAA,CAAAmB,UAAA,KAAA4H,yDAAA,kBAA8D;IAGhE/I,EAAA,CAAAW,YAAA,EAAM;IAIJX,EADF,CAAAC,cAAA,eAAwB,kBACmB;IACvCD,EAAA,CAAAe,SAAA,cAAkC;IAClCf,EAAA,CAAAU,MAAA,kBAAS;IAAAV,EAAA,CAAAe,SAAA,gBAA8B;IACzCf,EAAA,CAAAW,YAAA,EAAQ;IACRX,EAAA,CAAAC,cAAA,kBAIwE;IAA1CD,EAAA,CAAAE,UAAA,kBAAA8I,0EAAA;MAAAhJ,EAAA,CAAAI,aAAA,CAAAwI,IAAA;MAAA,MAAAtI,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAQF,MAAA,CAAAY,kBAAA,CAAmB,UAAU,CAAC;IAAA,EAAC;IAJrElB,EAAA,CAAAW,YAAA,EAIwE;IACxEX,EAAA,CAAAmB,UAAA,KAAA8H,yDAAA,kBAAiE;IAGnEjJ,EAAA,CAAAW,YAAA,EAAM;IAIJX,EADF,CAAAC,cAAA,eAAwB,kBAC0B;IAC9CD,EAAA,CAAAe,SAAA,cAAkC;IAClCf,EAAA,CAAAU,MAAA,0BAAiB;IAAAV,EAAA,CAAAe,SAAA,gBAA8B;IACjDf,EAAA,CAAAW,YAAA,EAAQ;IACRX,EAAA,CAAAC,cAAA,kBAEqF;IAAvDD,EAAA,CAAAE,UAAA,kBAAAgJ,0EAAA;MAAAlJ,EAAA,CAAAI,aAAA,CAAAwI,IAAA;MAAA,MAAAtI,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAQF,MAAA,CAAAY,kBAAA,CAAmB,uBAAuB,CAAC;IAAA,EAAC;IAFlFlB,EAAA,CAAAW,YAAA,EAEqF;IAIrFX,EAHA,CAAAmB,UAAA,KAAAgI,yDAAA,kBAA8E,KAAAC,yDAAA,kBAGzB;IAGvDpJ,EAAA,CAAAW,YAAA,EAAM;IAIJX,EADF,CAAAC,cAAA,eAAwB,gBACE;IACtBD,EAAA,CAAAe,SAAA,kBACsD;IACtDf,EAAA,CAAAC,cAAA,kBAAiD;IAC/CD,EAAA,CAAAU,MAAA,6CAAoC;IAAAV,EAAA,CAAAe,SAAA,gBAA8B;IAEtEf,EADE,CAAAW,YAAA,EAAQ,EACJ;IACNX,EAAA,CAAAmB,UAAA,KAAAkI,yDAAA,kBAAmE;IAGrErJ,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAmB,UAAA,KAAAmI,yDAAA,kBAAoF;IAKpFtJ,EAAA,CAAAC,cAAA,kBACmF;IAA1BD,EAAA,CAAAE,UAAA,mBAAAqJ,4EAAA;MAAAvJ,EAAA,CAAAI,aAAA,CAAAwI,IAAA;MAAA,MAAAtI,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAkJ,aAAA,EAAe;IAAA,EAAC;IAChFxJ,EAAA,CAAAmB,UAAA,KAAAsI,0DAAA,mBAAiG;IACjGzJ,EAAA,CAAAU,MAAA,IACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAU,MAAA,oBAAW;IAAAV,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAU,MAAA,kBAAU;IAEpDV,EAFoD,CAAAW,YAAA,EAAO,EACnD,EACF;;;;IApFAX,EAAA,CAAAY,SAAA,GAA4C;IAA5CZ,EAAA,CAAA0B,WAAA,eAAApB,MAAA,CAAAqB,cAAA,UAA4C;IAExC3B,EAAA,CAAAY,SAAA,EAA6B;IAA7BZ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAqB,cAAA,UAA6B;IAYjC3B,EAAA,CAAAY,SAAA,GAA4C;IAA5CZ,EAAA,CAAA0B,WAAA,eAAApB,MAAA,CAAAqB,cAAA,UAA4C;IAExC3B,EAAA,CAAAY,SAAA,EAA6B;IAA7BZ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAqB,cAAA,UAA6B;IAYjC3B,EAAA,CAAAY,SAAA,GAA+C;IAA/CZ,EAAA,CAAA0B,WAAA,eAAApB,MAAA,CAAAqB,cAAA,aAA+C;IAI3C3B,EAAA,CAAAY,SAAA,EAAgC;IAAhCZ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAqB,cAAA,aAAgC;IAYpC3B,EAAA,CAAAY,SAAA,GAA8E;IAA9EZ,EAAA,CAAA0B,WAAA,eAAApB,MAAA,CAAAqB,cAAA,6BAAArB,MAAA,CAAAmI,YAAA,GAA8E;IAE1EzI,EAAA,CAAAY,SAAA,EAA6C;IAA7CZ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAqB,cAAA,0BAA6C;IAG7C3B,EAAA,CAAAY,SAAA,EAAoB;IAApBZ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAmI,YAAA,GAAoB;IAStBzI,EAAA,CAAAY,SAAA,GAAiD;IAAjDZ,EAAA,CAAA0B,WAAA,eAAApB,MAAA,CAAAqB,cAAA,eAAiD;IAK/C3B,EAAA,CAAAY,SAAA,GAAkC;IAAlCZ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAqB,cAAA,eAAkC;IAMpC3B,EAAA,CAAAY,SAAA,EAA+B;IAA/BZ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAoI,yBAAA,CAA+B;IAK0B1I,EAAA,CAAAY,SAAA,EAAwC;IAAxCZ,EAAA,CAAA0B,WAAA,YAAApB,MAAA,CAAAoJ,sBAAA,CAAwC;IACrG1J,EAAA,CAAA4B,UAAA,cAAAtB,MAAA,CAAAqJ,YAAA,MAAArJ,MAAA,CAAAoJ,sBAAA,CAAsD;IAC/C1J,EAAA,CAAAY,SAAA,EAA4B;IAA5BZ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAoJ,sBAAA,CAA4B;IACnC1J,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAoJ,sBAAA,iDACF;;;;;IAWE1J,EAFJ,CAAAC,cAAA,eAAiE,eAClC,eACD;IACxBD,EAAA,CAAAe,SAAA,aAA0C;IAC5Cf,EAAA,CAAAW,YAAA,EAAM;IAENX,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAU,MAAA,8BAAuB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAEtDX,EAAA,CAAAC,cAAA,aAA2B;IACzBD,EAAA,CAAAU,MAAA,+JAGF;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAEJX,EAAA,CAAAC,cAAA,eAAkC;IAGhCD,EAAA,CAAAe,SAAA,eAAqF;IACvFf,EAAA,CAAAW,YAAA,EAAM;IAENX,EAAA,CAAAC,cAAA,mBAAsG;IACpGD,EAAA,CAAAU,MAAA,yBACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;IAGPX,EADF,CAAAC,cAAA,gBAA6B,iBACH;IAAAD,EAAA,CAAAU,MAAA,2DAAmD;IAGjFV,EAHiF,CAAAW,YAAA,EAAO,EAC9E,EACF,EACF;;;IAR+DX,EAAA,CAAAY,SAAA,IAAoC;IAApCZ,EAAA,CAAA4B,UAAA,eAAA5B,EAAA,CAAA4J,eAAA,IAAAC,GAAA,EAAoC;;;ADngB7G,MAAMC,sBAAsB,GAA6B;EACvDC,8BAA8B,EAAE,CAC9B,8CAA8C,EAC9C,4DAA4D,EAC5D,kEAAkE,EAClE,6DAA6D,EAC7D,2DAA2D,EAC3D,uDAAuD,CACxD;EACD;EACA;EACA;EACA;EACA;EACAC,uBAAuB,EAAE,CACvB,6CAA6C,EAC7C,wCAAwC,EACxC,iEAAiE,CAClE;EACDC,sBAAsB,EAAE,CACtB,6CAA6C,EAC7C,wCAAwC,EACxC,iEAAiE,CAClE;EACDC,wBAAwB,EAAE,CACxB,mCAAmC,EACnC,iDAAiD,EACjD,uDAAuD,EACvD,kDAAkD,EAClD,mCAAmC,CACpC;EACDC,uBAAuB,EAAE,CACvB,kCAAkC,EAClC,kCAAkC,EAClC,sDAAsD;CAEzD;AAgBD,OAAM,MAAOC,kCAAkC;EAwCnCC,EAAA;EACAC,eAAA;EACAC,EAAA;EACAC,qBAAA;EA1CAC,MAAM,GAAG,IAAI3K,YAAY,EAAQ;EACjC4K,UAAU,GAAG,IAAI5K,YAAY,EAA0B;EAEjE6K,gBAAgB;EAChBC,WAAW,GAAG,CAAC;EACNC,UAAU,GAAG,CAAC;EACvBC,aAAa,GAA8B,EAAE;EAC7C/G,UAAU,GAAG,EAAE;EACfgH,kBAAkB,GAAa,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EACnDvI,SAAS,GAAW,EAAE;EACtBc,gBAAgB,GAAY,KAAK;EACjCzB,gBAAgB,GAAY,KAAK;EACjC0B,iBAAiB,GAAY,KAAK;EAClCmG,sBAAsB,GAAY,KAAK;EACvC9G,eAAe,GAAW,EAAE;EAC5B8F,yBAAyB,GAAW,EAAE;EAEtC;EACA,OAAOsC,SAAS,GAAGjL,UAAU,CAACkL,OAAO,CAAC,WAAW,CAAC;EAClD,OAAOC,mBAAmB,GAAGnL,UAAU,CAACkL,OAAO,CAAC,iFAAiF,CAAC;EAClI,OAAOE,YAAY,GAAGpL,UAAU,CAACkL,OAAO,CAAC,wDAAwD,CAAC;EAClG,OAAOG,eAAe,GAAGrL,UAAU,CAACkL,OAAO,CAAC,iCAAiC,CAAC;EAE9E;EACAxE,MAAM,GAAU,EAAE;EAClBE,KAAK,GAAU,EAAE;EACjBE,QAAQ,GAAU,EAAE;EACpBL,cAAc,GAAW,CAAC;EAC1B6E,gBAAgB,GAAG,EAAE;EACrB3E,cAAc,GAAW,CAAC;EAC1B4E,gBAAgB,GAAG,EAAE;EACrB1E,iBAAiB,GAAW,CAAC;EAC7B2E,mBAAmB,GAAG,EAAE;EACxBC,SAAS;EAET;EACAhD,YAAY,GAA0B,EAAE;EAExCiD,YACUpB,EAAe,EACfC,eAAgC,EAChCC,EAAqB,EACrBC,qBAA4C;IAH5C,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,qBAAqB,GAArBA,qBAAqB;IAE7B,IAAI,CAACG,gBAAgB,GAAG,IAAI,CAACe,UAAU,EAAE;IAEzC,IAAI,CAACF,SAAS,GAAG,IAAI,CAACnB,EAAE,CAACsB,KAAK,CAAC;MAC7BC,MAAM,EAAE,CAAC,CAAC,CAAC;MACXC,MAAM,EAAE,CAAC,CAAC,CAAC;MACXC,SAAS,EAAC,CAAC,CAAC;KACb,CAAC;IAEF,IAAI,CAACC,yBAAyB,EAAE;IAChC,IAAI,CAACC,UAAU,EAAE;EACnB;EAEQN,UAAUA,CAAA;IAChB,OAAO,IAAI,CAACrB,EAAE,CAACsB,KAAK,CAAC;MACnB;MACAM,QAAQ,EAAE,CACR,EAAE,EACF,CACElM,UAAU,CAACmM,QAAQ,EACnBnM,UAAU,CAACoM,SAAS,CAAC,CAAC,CAAC,EACvB/B,kCAAkC,CAACY,SAAS,CAC7C,CACF;MACDoB,KAAK,EAAE,CACL,EAAE,EACF,CAACrM,UAAU,CAACmM,QAAQ,EAAE9B,kCAAkC,CAACc,mBAAmB,CAAC,CAC9E;MAED;MACAmB,gBAAgB,EAAE,IAAI,CAAChC,EAAE,CAACiC,KAAK,CAC7BC,KAAK,CAAC,CAAC,CAAC,CACLC,IAAI,CAAC,EAAE,CAAC,CACRC,GAAG,CAAC,MACH,IAAI,CAACpC,EAAE,CAACqC,OAAO,CAAC,EAAE,EAAE,CAClB3M,UAAU,CAACmM,QAAQ,EACnBnM,UAAU,CAACkL,OAAO,CAAC,OAAO,CAAC,CAC5B,CAAC,CACH,CACJ;MAED;MACA0B,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC5M,UAAU,CAACmM,QAAQ,EAAE9B,kCAAkC,CAACe,YAAY,CAAC,CAAC;MACnFyB,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC7M,UAAU,CAAC6M,KAAK,CAAC,CAAC;MAC/BC,QAAQ,EAAE,CACR,EAAE,EACF,CACE9M,UAAU,CAACmM,QAAQ,EACnBnM,UAAU,CAACoM,SAAS,CAAC,CAAC,CAAC,EACvB/B,kCAAkC,CAACgB,eAAe,CACnD,CACF;MACD0B,qBAAqB,EAAE,CAAC,EAAE,EAAE,CAAC/M,UAAU,CAACmM,QAAQ,CAAC,CAAC;MAClDa,UAAU,EAAE,CAAC,KAAK,EAAE,CAAChN,UAAU,CAACiN,YAAY,CAAC;KAC9C,CAAC;EACJ;EAEQjB,yBAAyBA,CAAA;IAC7B,IAAI,CAACvD,YAAY,GAAGyE,MAAM,CAACC,OAAO,CAACpD,sBAAsB,CAAC,CAAC2C,GAAG,CAAC,CAAC,CAACU,KAAK,EAAE3F,eAAe,CAAC,MAAM;MAC9FS,oBAAoB,EAAEkF,KAAK;MAC3B3F,eAAe;MACfW,QAAQ,EAAE,KAAK;MACfH,QAAQ,EAAE,KAAK;MACfZ,aAAa,EAAE;KAChB,CAAC,CAAC;EACL;EAEAtD,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC8G,WAAW,GAAG,IAAI,CAACC,UAAU,EAAE;MACtC;MACA,IAAI,IAAI,CAACD,WAAW,KAAK,CAAC,EAAE;QAC1B,IAAI,CAAChI,eAAe,GAAG,EAAE;MAC3B;MACA,IAAI,CAACgI,WAAW,EAAE;IACpB;EACF;EAEArE,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACqE,WAAW,GAAG,IAAI,CAACC,UAAU,EAAE;MACtC;MACA,IAAI,IAAI,CAACD,WAAW,KAAK,CAAC,EAAE;QAC1B,IAAI,CAAChI,eAAe,GAAG,EAAE;MAC3B;MAEA,IAAG,IAAI,CAACgI,WAAW,IAAI,CAAC,EAAC;QACvB,IAAI,CAACA,WAAW,GAAG,CAAC;MACtB;MACA,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EAEAnK,YAAYA,CAAA;IACV,IAAI,IAAI,CAACmK,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;IACpB,CAAC,MAAM;MACL,IAAI,CAACH,MAAM,CAAC2C,IAAI,EAAE;IACpB;EACF;EAEAhJ,YAAYA,CAACiJ,KAAY,EAAEC,QAAgB;IACzC,MAAMC,KAAK,GAAIF,KAAK,CAACG,MAA2B,CAACD,KAAK;IACtD,IAAI,CAACA,KAAK,EAAEE,MAAM,EAAE;IAEpB;IACA,IAAI,CAAC3C,aAAa,CAACwC,QAAQ,CAAC,GAAGf,KAAK,CAACmB,IAAI,CAACH,KAAK,CAAC;IAChDI,OAAO,CAACC,GAAG,CAAC,YAAYL,KAAK,CAACE,MAAM,gBAAgBH,QAAQ,4BAA4B,CAAC;EAC3F;EAEAtJ,YAAYA,CAACsJ,QAAgB;IAC3B,OAAO,IAAI,CAACxC,aAAa,CAACwC,QAAQ,CAAC,EAAEG,MAAM,IAAI,CAAC;EAClD;EAEA;EACAI,UAAUA,CAACP,QAAgB;IACzB,OAAO,IAAI,CAACxC,aAAa,CAACwC,QAAQ,CAAC;IACnCK,OAAO,CAACC,GAAG,CAAC,yBAAyBN,QAAQ,EAAE,CAAC;EAClD;EAEA;EACAQ,aAAaA,CAAA;IACX,IAAI,CAAChD,aAAa,GAAG,EAAE;IACvB6C,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;EAC3C;EAEA;EACA5B,UAAUA,CAAA;IACR,IAAI,CAAC1B,eAAe,CAACyD,SAAS,EAAE,CAACC,SAAS,CAAC;MACzCC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,IAAI,EAAE;UAC7B,IAAI,CAAC1H,MAAM,GAAGyH,QAAQ,CAACC,IAAI;QAC7B,CAAC,MAAM;UACLR,OAAO,CAACS,IAAI,CAAC,4BAA4B,CAAC;UAC1C,IAAI,CAAC3H,MAAM,GAAG,EAAE;QAClB;MACF,CAAC;MACD4H,KAAK,EAAGC,GAAQ,IAAI;QAClBX,OAAO,CAACU,KAAK,CAAC,uBAAuB,EAAEC,GAAG,CAAC;MAC7C,CAAC;MACDC,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAAChE,EAAE,CAACiE,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAEAC,SAASA,CAAC7C,MAAe;IACvB,IAAI,CAACtB,eAAe,CAACoE,QAAQ,CAAC9C,MAAM,CAAC,CAACoC,SAAS,CAAC;MAC9CC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,IAAI,EAAE;UAC7B,IAAI,CAACxH,KAAK,GAAGuH,QAAQ,CAACC,IAAI;QAC5B,CAAC,MAAM;UACLR,OAAO,CAACS,IAAI,CAAC,2BAA2B,CAAC;UACzC,IAAI,CAACzH,KAAK,GAAG,EAAE;QACjB;MACF,CAAC;MACD0H,KAAK,EAAGC,GAAQ,IAAI;QAClBX,OAAO,CAACU,KAAK,CAAC,sBAAsB,EAAEC,GAAG,CAAC;QAC1C,IAAI,CAAC3H,KAAK,GAAG,EAAE;MACjB,CAAC;MACD4H,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAAChE,EAAE,CAACiE,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAEAG,YAAYA,CAAC9C,MAAe;IAC1B,IAAI,CAACvB,eAAe,CAACsE,WAAW,CAAC/C,MAAM,CAAC,CAACmC,SAAS,CAAC;MACjDC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,IAAI,EAAE;UAC7B,IAAI,CAACtH,QAAQ,GAAGqH,QAAQ,CAACC,IAAI;QAC/B,CAAC,MAAM;UACLR,OAAO,CAACS,IAAI,CAAC,2BAA2B,CAAC;UACzC,IAAI,CAACvH,QAAQ,GAAG,EAAE;QACpB;MACF,CAAC;MACDwH,KAAK,EAAGC,GAAQ,IAAI;QAClBX,OAAO,CAACU,KAAK,CAAC,sBAAsB,EAAEC,GAAG,CAAC;QAC1C,IAAI,CAACzH,QAAQ,GAAG,EAAE;MACpB,CAAC;MACD0H,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAAChE,EAAE,CAACiE,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAEA3I,YAAYA,CAACwH,KAAU;IACrB,MAAMzB,MAAM,GAAGyB,KAAK,CAACG,MAAM,CAACqB,KAAK;IACjC,IAAI,CAACrI,cAAc,GAAGoF,MAAM;IAC5B,IAAI,CAACP,gBAAgB,GACnBgC,KAAK,CAACG,MAAM,CAACsB,OAAO,CAACzB,KAAK,CAACG,MAAM,CAACuB,aAAa,CAAC,CAACC,IAAI;IACvD,IAAI,CAACxD,SAAS,CAACyD,UAAU,CAAC;MAAErD,MAAM,EAAEA;IAAM,CAAE,CAAC;IAE7C;IACA,IAAI,CAAClF,cAAc,GAAG,CAAC;IACvB,IAAI,CAAC4E,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACE,SAAS,CAACyD,UAAU,CAAC;MAAEpD,MAAM,EAAE;IAAC,CAAE,CAAC;IACxC,IAAI,CAAClF,KAAK,GAAG,EAAE;IAEf,IAAI,CAAC8H,SAAS,CAAC7C,MAAM,CAAC;EACxB;EAEA5F,YAAYA,CAACqH,KAAU;IACrB,MAAMxB,MAAM,GAAGwB,KAAK,CAACG,MAAM,CAACqB,KAAK;IACjC,IAAI,CAACnI,cAAc,GAAGmF,MAAM;IAC5B,IAAI,CAACP,gBAAgB,GACnB+B,KAAK,CAACG,MAAM,CAACsB,OAAO,CAACzB,KAAK,CAACG,MAAM,CAACuB,aAAa,CAAC,CAACC,IAAI;IACvD,IAAI,CAACxD,SAAS,CAACyD,UAAU,CAAC;MAAEpD,MAAM,EAAEA;IAAM,CAAE,CAAC;IAE7C,IAAI,CAAC8C,YAAY,CAAC9C,MAAM,CAAC;EAC3B;EAEC1F,eAAeA,CAACkH,KAAU;IACzB,MAAMvB,SAAS,GAAGuB,KAAK,CAACG,MAAM,CAACqB,KAAK;IACpC,IAAI,CAACjI,iBAAiB,GAAGkF,SAAS;IAClC,IAAI,CAACP,mBAAmB,GAAG8B,KAAK,CAACG,MAAM,CAACsB,OAAO,CAACzB,KAAK,CAACG,MAAM,CAACuB,aAAa,CAAC,CAACC,IAAI;IAChF,IAAI,CAACxD,SAAS,CAACyD,UAAU,CAAC;MAAEnD,SAAS,EAAEA;IAAS,CAAE,CAAC;EACrD;EAEA;EACAjE,yBAAyBA,CAACsF,KAA0B;IAClD,IAAI,CAAC3E,YAAY,CAAC0G,OAAO,CAAEC,CAAC,IAAI;MAC9B,IAAIA,CAAC,KAAKhC,KAAK,EAAEgC,CAAC,CAAChH,QAAQ,GAAG,KAAK;IACrC,CAAC,CAAC;IACFgF,KAAK,CAAChF,QAAQ,GAAG,CAACgF,KAAK,CAAChF,QAAQ;EAClC;EAEAR,sBAAsBA,CAACwF,KAA0B,EAAEE,KAAU;IAC3DF,KAAK,CAACnF,QAAQ,GAAGqF,KAAK,CAACG,MAAM,CAAC4B,OAAO;IACrCzB,OAAO,CAACC,GAAG,CAACT,KAAK,CAAC;EACpB;EAEAhG,sBAAsBA,CAACgG,KAA0B,EAAEkC,IAAY,EAAEhC,KAAU;IACzE,IAAI,CAACF,KAAK,CAAC/F,aAAa,EAAE;MACxB+F,KAAK,CAAC/F,aAAa,GAAG,EAAE;IAC1B;IAEA,IAAIiG,KAAK,CAACG,MAAM,CAAC4B,OAAO,EAAE;MACxB,IAAI,CAACjC,KAAK,CAAC/F,aAAa,CAACC,QAAQ,CAACgI,IAAI,CAAC,EAAE;QACvClC,KAAK,CAAC/F,aAAa,CAACkI,IAAI,CAACD,IAAI,CAAC;MAChC;IACF,CAAC,MAAM;MACLlC,KAAK,CAAC/F,aAAa,GAAG+F,KAAK,CAAC/F,aAAa,CAACmI,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKH,IAAI,CAAC;IACnE;IAEA1B,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC6B,8BAA8B,EAAE,CAAC;EACpD;EAEA;EACA1H,oBAAoBA,CAAC2H,YAAiC;IACpD,OAAO,IAAI,CAAClH,YAAY,CAACmH,IAAI,CAC1BxC,KAAK,IAAKA,KAAK,KAAKuC,YAAY,IAAIvC,KAAK,CAAChF,QAAQ,CACpD;EACH;EAEA;EACAxE,gBAAgBA,CAAC0L,IAAY;IAC3B,IAAI,CAACtL,UAAU,GAAGsL,IAAI;IACtB1B,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC7J,UAAU,CAAC;EAC9B;EAEA,IAAIV,wBAAwBA,CAAA;IAC1B,OAAQ,IAAI,CAACsH,gBAAgB,CAACiF,GAAG,CAAC,kBAAkB,CAAe,CAChEC,QAAQ;EACb;EAEAC,YAAYA,CAAC3N,KAAa;IACxB,MAAM4N,IAAI,GAAG,IAAI,CAAChF,kBAAkB,CAACiF,IAAI,CAAC,EAAE,CAAC;IAC7C,IAAI,CAACrF,gBAAgB,CAACsE,UAAU,CAAC;MAAE5C,gBAAgB,EAAE0D;IAAI,CAAE,CAAC;EAC9D;EAEA;EACApO,cAAcA,CAACsO,SAAiB;IAC9B,MAAMC,KAAK,GAAG,IAAI,CAACvF,gBAAgB,CAACiF,GAAG,CAACK,SAAS,CAAC;IAClD,OAAO,CAAC,EAAEC,KAAK,IAAIA,KAAK,CAAC5N,OAAO,KAAK4N,KAAK,CAACC,KAAK,IAAID,KAAK,CAAC3N,OAAO,CAAC,CAAC;EACrE;EAEArB,kBAAkBA,CAAC+O,SAAiB;IAClC,IAAI,CAACtF,gBAAgB,CAACiF,GAAG,CAACK,SAAS,CAAC,EAAEG,aAAa,EAAE;EACvD;EAEAtP,aAAaA,CAACmP,SAAiB;IAC7B,MAAMC,KAAK,GAAG,IAAI,CAACvF,gBAAgB,CAACiF,GAAG,CAACK,SAAS,CAAC;IAClD,IAAI,CAACC,KAAK,EAAEG,MAAM,EAAE,OAAO,EAAE;IAE7B,MAAMA,MAAM,GAAGH,KAAK,CAACG,MAAM;IAC3B,IAAIA,MAAM,CAAC,UAAU,CAAC,EAAE,OAAO,wBAAwB;IACvD,IAAIA,MAAM,CAAC,SAAS,CAAC,IAAIJ,SAAS,KAAK,UAAU,EAAE,OAAO,6BAA6B;IACvF,IAAII,MAAM,CAAC,SAAS,CAAC,IAAIJ,SAAS,KAAK,OAAO,EAAE,OAAO,mCAAmC;IAC1F,IAAII,MAAM,CAAC,SAAS,CAAC,IAAIJ,SAAS,KAAK,OAAO,EAAE,OAAO,0BAA0B;IACjF,IAAII,MAAM,CAAC,SAAS,CAAC,IAAIJ,SAAS,KAAK,UAAU,EAAE,OAAO,oCAAoC;IAC9F,IAAII,MAAM,CAAC,OAAO,CAAC,EAAE,OAAO,mBAAmB;IAC/C,IAAIA,MAAM,CAAC,WAAW,CAAC,EAAE,OAAO,OAAOA,MAAM,CAAC,WAAW,CAAC,CAACC,cAAc,QAAQ;IAEjF,OAAO,eAAe;EACxB;EAEA7H,YAAYA,CAAA;IACV,MAAMoE,QAAQ,GAAG,IAAI,CAAClC,gBAAgB,CAACiF,GAAG,CAAC,UAAU,CAAC,EAAEf,KAAK;IAC7D,MAAM0B,eAAe,GAAG,IAAI,CAAC5F,gBAAgB,CAACiF,GAAG,CAAC,uBAAuB,CAAC,EAAEf,KAAK;IAEjF,IAAIhC,QAAQ,IAAI0D,eAAe,IAAI1D,QAAQ,KAAK0D,eAAe,EAAE;MAC/D,OAAO,wBAAwB;IACjC;IACA,OAAO,EAAE;EACX;EAEA;EACAzO,YAAYA,CAAA;IACV,MAAMmK,QAAQ,GAAG,IAAI,CAACtB,gBAAgB,CAACiF,GAAG,CAAC,UAAU,CAAC;IACtD,MAAMxD,KAAK,GAAG,IAAI,CAACzB,gBAAgB,CAACiF,GAAG,CAAC,OAAO,CAAC;IAEhD,OAAO,CAAC,EAAE3D,QAAQ,EAAEuE,KAAK,IAAIpE,KAAK,EAAEoE,KAAK,CAAC;EAC5C;EAEAhN,YAAYA,CAAA;IACV,MAAM6I,gBAAgB,GAAG,IAAI,CAAC1B,gBAAgB,CAACiF,GAAG,CAAC,kBAAkB,CAAc;IACnF,OAAOvD,gBAAgB,CAACmE,KAAK;EAC/B;EAEA7G,YAAYA,CAAA;IACV,MAAMgD,KAAK,GAAG,IAAI,CAAChC,gBAAgB,CAACiF,GAAG,CAAC,OAAO,CAAC;IAChD,MAAMhD,KAAK,GAAG,IAAI,CAACjC,gBAAgB,CAACiF,GAAG,CAAC,OAAO,CAAC;IAChD,MAAM/C,QAAQ,GAAG,IAAI,CAAClC,gBAAgB,CAACiF,GAAG,CAAC,UAAU,CAAC;IACtD,MAAMa,oBAAoB,GAAG,IAAI,CAAC9F,gBAAgB,CAACiF,GAAG,CAAC,uBAAuB,CAAC;IAC/E,MAAM7C,UAAU,GAAG,IAAI,CAACpC,gBAAgB,CAACiF,GAAG,CAAC,YAAY,CAAC;IAE1D;IACA,MAAMc,cAAc,GAAG7D,QAAQ,EAAEgC,KAAK,KAAK4B,oBAAoB,EAAE5B,KAAK;IAEtE,OAAO,CAAC,EACNlC,KAAK,EAAE6D,KAAK,IACZ5D,KAAK,EAAE4D,KAAK,IACZ3D,QAAQ,EAAE2D,KAAK,IACfC,oBAAoB,EAAED,KAAK,IAC3BzD,UAAU,EAAEyD,KAAK,IACjBE,cAAc,CACf;EACH;EAEAlP,yBAAyBA,CAAA;IACvB,IAAI,CAACmP,oBAAoB,CAAC,IAAI,CAAC;EACjC;EAEAA,oBAAoBA,CAACC,cAAA,GAA0B,KAAK;IAClD,IAAI,CAAC,IAAI,CAAC9O,YAAY,EAAE,EAAE;MACxB,IAAI,CAACZ,kBAAkB,CAAC,UAAU,CAAC;MACnC,IAAI,CAACA,kBAAkB,CAAC,OAAO,CAAC;MAChC;IACF;IAEA,IAAI,CAACW,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACe,eAAe,GAAG,EAAE;IACzB,MAAMwJ,KAAK,GAAG,IAAI,CAACzB,gBAAgB,CAACiF,GAAG,CAAC,OAAO,CAAC,EAAEf,KAAK,EAAEgC,IAAI,EAAE;IAE/D,IAAIC,MAAM,GAAuC,EAAE;IACnD,MAAMC,OAAO,GAAG,4BAA4B,CAACC,IAAI,CAAC5E,KAAK,CAAC;IACxD,MAAM6E,OAAO,GAAG,oBAAoB,CAACD,IAAI,CAAC5E,KAAK,CAAC;IAEhD,IAAI2E,OAAO,EAAE;MACXD,MAAM,CAAClE,KAAK,GAAGR,KAAK;IACtB,CAAC,MAAM,IAAI6E,OAAO,EAAE;MAClBH,MAAM,CAACnE,KAAK,GAAGP,KAAK;IACtB;IAEA,IAAI,CAAC5B,qBAAqB,CAAC0G,OAAO,CAACJ,MAAM,CAAC,CAAC9C,SAAS,CAAC;MACnDC,IAAI,EAAGC,QAAa,IAAI;QACtBP,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEM,QAAQ,CAAC;QAClC,IAAI,CAACrM,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACsP,cAAc,EAAE;QACrB,IAAIP,cAAc,EAAE;UAClB,IAAI,CAAC9M,QAAQ,EAAE;QACjB;QACA,IAAI,CAACyG,EAAE,CAAC6G,YAAY,EAAE;MACxB,CAAC;MACD/C,KAAK,EAAGA,KAAU,IAAI;QACpBV,OAAO,CAACU,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C,IAAI,CAACxM,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACe,eAAe,GAAGyL,KAAK,CAACgD,OAAO,IAAI,qDAAqD;QAC7F,IAAI,CAAC9G,EAAE,CAAC6G,YAAY,EAAE;MACxB;KACD,CAAC;EACJ;EAEAjO,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACK,YAAY,EAAE,EAAE;MACxB;MACA,MAAM8N,qBAAqB,GAAG,IAAI,CAAC3G,gBAAgB,CAACiF,GAAG,CAAC,kBAAkB,CAAc;MACxF0B,qBAAqB,CAACzB,QAAQ,CAACX,OAAO,CAACxC,OAAO,IAAIA,OAAO,CAAC0D,aAAa,EAAE,CAAC;MAC1E;IACF;IAEA,IAAI,CAAC7M,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACX,eAAe,GAAG,EAAE;IACzB,MAAMwJ,KAAK,GAAG,IAAI,CAACzB,gBAAgB,CAACiF,GAAG,CAAC,OAAO,CAAC,EAAEf,KAAK,EAAEgC,IAAI,EAAE;IAC/D,MAAMU,SAAS,GAAG,IAAI,CAAC5G,gBAAgB,CAACiF,GAAG,CAAC,kBAAkB,CAAC,EAAEf,KAAK;IACtE,MAAM2C,GAAG,GAAGD,SAAS,CAACvB,IAAI,CAAC,EAAE,CAAC;IAE9B,IAAIc,MAAM,GAAqD,EAAE;IACjE,MAAMC,OAAO,GAAG,4BAA4B,CAACC,IAAI,CAAC5E,KAAK,CAAC;IACxD,MAAM6E,OAAO,GAAG,oBAAoB,CAACD,IAAI,CAAC5E,KAAK,CAAC;IAEhD,IAAI2E,OAAO,EAAE;MACXD,MAAM,CAAClE,KAAK,GAAGR,KAAK;IACtB,CAAC,MAAM,IAAI6E,OAAO,EAAE;MAClBH,MAAM,CAACnE,KAAK,GAAGP,KAAK;IACtB;IAEA0E,MAAM,CAACU,GAAG,GAAGA,GAAG;IAEhB,IAAI,CAAChH,qBAAqB,CAACiH,QAAQ,CAACX,MAAM,CAAC,CAAC9C,SAAS,CAAC;MACpDC,IAAI,EAAGC,QAAa,IAAI;QACtBP,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEM,QAAQ,CAAC;QACrC,IAAI,CAAC3K,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAACO,QAAQ,EAAE;QACf,IAAI,CAACyG,EAAE,CAAC6G,YAAY,EAAE;MACxB,CAAC;MACD/C,KAAK,EAAGA,KAAU,IAAI;QACpBV,OAAO,CAACU,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAAC9K,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAACX,eAAe,GAAGyL,KAAK,EAAEA,KAAK,EAAEgD,OAAO,IAAI,8CAA8C;QAC9F,IAAI,CAAC9G,EAAE,CAAC6G,YAAY,EAAE;MACxB;KACD,CAAC;EACJ;EAEA5H,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAACG,YAAY,EAAE,EAAE;MACxB,IAAI,CAACzI,kBAAkB,CAAC,OAAO,CAAC;MAChC,IAAI,CAACA,kBAAkB,CAAC,OAAO,CAAC;MAChC,IAAI,CAACA,kBAAkB,CAAC,UAAU,CAAC;MACnC,IAAI,CAACA,kBAAkB,CAAC,uBAAuB,CAAC;MAChD,IAAI,CAACA,kBAAkB,CAAC,YAAY,CAAC;MACrC;IACF;IAEA,IAAI,CAACwI,sBAAsB,GAAG,IAAI;IAClC,IAAI,CAAChB,yBAAyB,GAAG,EAAE;IACnC,MAAM0D,KAAK,GAAG,IAAI,CAACzB,gBAAgB,CAACiF,GAAG,CAAC,OAAO,CAAC,EAAEf,KAAK,EAAEgC,IAAI,EAAE;IAC/D,IAAIC,MAAM,GAAG,IAAI,CAACnG,gBAAgB,CAACkE,KAAK;IACxCiC,MAAM,CAACY,IAAI,GAAG,QAAQ;IACtBZ,MAAM,CAACzB,IAAI,GAAG,IAAI,CAACtL,UAAU;IAC7B,MAAMgN,OAAO,GAAG,4BAA4B,CAACC,IAAI,CAAC5E,KAAK,CAAC;IACxD,MAAM6E,OAAO,GAAG,oBAAoB,CAACD,IAAI,CAAC5E,KAAK,CAAC;IAEhD,IAAI2E,OAAO,EAAE;MACXD,MAAM,CAAClE,KAAK,GAAGR,KAAK;IACtB,CAAC,MAAM,IAAI6E,OAAO,EAAE;MAClBH,MAAM,CAACnE,KAAK,GAAGP,KAAK;IACtB;IAEA,MAAMuF,oBAAoB,GAAG,IAAI,CAAClC,8BAA8B,EAAE;IAClE,MAAMmC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAE/B;IACA,KAAK,MAAMC,GAAG,IAAIhB,MAAM,EAAE;MACxB,IAAIA,MAAM,CAACiB,cAAc,CAACD,GAAG,CAAC,EAAE;QAC9BF,QAAQ,CAACI,MAAM,CAACF,GAAG,EAAEhB,MAAM,CAACgB,GAAG,CAAC,CAAC;MACnC;IACF;IAEA,IAAG,IAAI,CAACpL,cAAc,IAAI,CAAC,EAAC;MAC1BoK,MAAM,CAACmB,OAAO,GAAG,CAAC,IAAI,CAACvL,cAAc,CAAC;MACtCoK,MAAM,CAACmB,OAAO,EAAE/C,OAAO,CAAE5J,EAAO,IAAKsM,QAAQ,CAACI,MAAM,CAAC,WAAW,EAAE1M,EAAE,CAAC,CAAC;IACxE;IAEA,IAAG,IAAI,CAACsB,iBAAiB,IAAI,CAAC,EAAC;MAC7BkK,MAAM,CAACjK,QAAQ,GAAG,CAAC,IAAI,CAACD,iBAAiB,CAAC;MAC1CkK,MAAM,CAACjK,QAAQ,EAAEqI,OAAO,CAAE5J,EAAO,IAAKsM,QAAQ,CAACI,MAAM,CAAC,YAAY,EAAE1M,EAAE,CAAC,CAAC;IAC1E;IAEA;IACA,KAAK,MAAMwM,GAAG,IAAIH,oBAAoB,EAAE;MACtC,MAAMO,MAAM,GAAGP,oBAAoB,CAACG,GAAG,CAAC;MACxCI,MAAM,CAAChD,OAAO,CAACL,KAAK,IAAG;QACrB+C,QAAQ,CAACI,MAAM,CAACF,GAAG,EAAEjD,KAAK,CAAC;MAC7B,CAAC,CAAC;IACJ;IAEA;IACA,KAAK,MAAMvB,QAAQ,IAAI,IAAI,CAACxC,aAAa,EAAE;MACzC,MAAMyC,KAAK,GAAG,IAAI,CAACzC,aAAa,CAACwC,QAAQ,CAAC;MAC1C,IAAIC,KAAK,EAAEE,MAAM,EAAE;QACjB;QACAF,KAAK,CAAC2B,OAAO,CAAEiD,IAAI,IAAI;UACrBP,QAAQ,CAACI,MAAM,CAAC1E,QAAQ,EAAE6E,IAAI,CAAC;QACjC,CAAC,CAAC;MACJ;IACF;IAEA,IAAI,CAAC3H,qBAAqB,CAAC4H,QAAQ,CAACR,QAAQ,CAAC,CAAC5D,SAAS,CAAC;MACtDC,IAAI,EAAGC,QAAa,IAAI;QACtBP,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEM,QAAQ,CAAC;QAChD,IAAImE,IAAI,GAAGnE,QAAQ,CAACC,IAAI;QACxBmE,YAAY,CAACC,OAAO,CAAC,WAAW,EAAEF,IAAI,CAACG,SAAS,CAAC;QACjD,IAAI,CAAChI,qBAAqB,CAACiI,cAAc,CAACvE,QAAQ,CAACC,IAAI,CAAC;QACxD,IAAI,CAACzE,sBAAsB,GAAG,KAAK;QACnC,IAAI,CAAC5F,QAAQ,EAAE;QACf,IAAI,CAACyG,EAAE,CAAC6G,YAAY,EAAE;MACxB,CAAC;MACD/C,KAAK,EAAGA,KAAU,IAAI;QACpBV,OAAO,CAACU,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C,IAAI,CAAC3E,sBAAsB,GAAG,KAAK;QACnC,IAAI,CAAChB,yBAAyB,GAAG2F,KAAK,EAAEA,KAAK,EAAEgD,OAAO,IAAI,6CAA6C;QACvG,IAAI,CAAC9G,EAAE,CAAC6G,YAAY,EAAE;MACxB;KACD,CAAC;EACJ;EAEAD,cAAcA,CAAA;IACZ,IAAI,CAAC7N,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACd,SAAS,GAAG,EAAE;IAEnB,MAAMkQ,UAAU,GAAGC,WAAW,CAAC,MAAK;MAClC,IAAI,CAACnQ,SAAS,EAAE;MAChB,IAAI,IAAI,CAACA,SAAS,KAAK,CAAC,EAAE;QACxBoQ,aAAa,CAACF,UAAU,CAAC;QACzB,IAAI,CAACpP,gBAAgB,GAAG,IAAI;MAC9B;MACA,IAAI,CAACiH,EAAE,CAAC6G,YAAY,EAAE;IACxB,CAAC,EAAE,IAAI,CAAC;EACV;EAEAhP,aAAaA,CAACiL,KAAU,EAAElL,KAAa;IACrC,MAAMiK,KAAK,GAAGiB,KAAK,CAACG,MAAM;IAC1B,IAAIpB,KAAK,CAACyC,KAAK,IAAI1M,KAAK,GAAG,CAAC,EAAE;MAC5B,MAAM0Q,SAAS,GACbzG,KAAK,CAAC0G,aAAa,EAAEC,kBAAkB,EAAEC,aAAa,CAAC,OAAO,CAAC;MACjEH,SAAS,EAAEI,KAAK,EAAE;IACpB;EACF;EAEQC,cAAcA,CAAA;IACpB,IAAI,CAACnI,kBAAkB,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC9C,MAAMuG,qBAAqB,GAAG,IAAI,CAAC3G,gBAAgB,CAACiF,GAAG,CACrD,kBAAkB,CACN;IACd0B,qBAAqB,CAACzB,QAAQ,CAACX,OAAO,CAAExC,OAAO,IAAI;MACjDA,OAAO,CAACyG,QAAQ,CAAC,EAAE,CAAC;MACpBzG,OAAO,CAAC0G,eAAe,EAAE;MACzB1G,OAAO,CAAC2G,cAAc,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEA1Q,YAAYA,CAAA;IACV,IAAI,CAACuQ,cAAc,EAAE;IACrB,IAAI,CAACtQ,eAAe,GAAG,EAAE;IACzB,IAAI,CAAC+N,oBAAoB,CAAC,KAAK,CAAC;EAClC;EAEA2C,WAAWA,CAACC,GAAW;IACrB,OAAOA,GAAG,CACPC,WAAW,EAAE,CACbC,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC,CAC3BA,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;EAC1B;EAEAhE,8BAA8BA,CAAA;IAC5B,MAAMiE,MAAM,GAAgC,EAAE;IAE9C,KAAK,MAAMvG,KAAK,IAAI,IAAI,CAAC3E,YAAY,EAAE;MACrC,IAAI2E,KAAK,CAAC/F,aAAa,IAAI+F,KAAK,CAAC/F,aAAa,CAACqG,MAAM,GAAG,CAAC,EAAE;QACzD,MAAMqE,GAAG,GAAG,IAAI,CAACwB,WAAW,CAACnG,KAAK,CAAClF,oBAAoB,CAAC;QACxDyL,MAAM,CAAC,wBAAwB5B,GAAG,KAAK,CAAC,GAAG3E,KAAK,CAAC/F,aAAa,CAACqF,GAAG,CAAC4C,IAAI,IACrE,IAAI,CAACiE,WAAW,CAACjE,IAAI,CAAC,CACvB;MACH;IACF;IAEA,OAAOqE,MAAM;EACf;EAEApM,wBAAwB,GAA8B;IACpD,8CAA8C,EAAE,gEAAgE;IAChH,4DAA4D,EAAE,uFAAuF;IACrJ,kEAAkE,EAAE,kEAAkE;IACtI,6DAA6D,EAAE,6DAA6D;IAC5H,2DAA2D,EAAE,qEAAqE;IAClI,uDAAuD,EAAE,kEAAkE;IAC3H,6CAA6C,EAAE,uDAAuD;IACtG,wCAAwC,EAAE,+CAA+C;IACzF,iEAAiE,EAAE,kEAAkE;IACrI,kCAAkC,EAAE,gEAAgE;IACpG,kCAAkC,EAAE,aAAa;IACjD,sDAAsD,EAAE,kEAAkE;IAC1H,mCAAmC,EAAE,yEAAyE;IAC9G,yCAAyC,EAAE,uFAAuF;IAClI,uDAAuD,EAAE,mFAAmF;IAC5I,kDAAkD,EAAE,wBAAwB;IAC5E,mCAAmC,EAAE;GACtC;;qCAxnBU8C,kCAAkC,EAAApK,EAAA,CAAA2T,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA7T,EAAA,CAAA2T,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA/T,EAAA,CAAA2T,iBAAA,CAAA3T,EAAA,CAAAgU,iBAAA,GAAAhU,EAAA,CAAA2T,iBAAA,CAAAM,EAAA,CAAAC,qBAAA;EAAA;;UAAlC9J,kCAAkC;IAAA+J,SAAA;IAAAC,OAAA;MAAA3J,MAAA;MAAAC,UAAA;IAAA;IAAA2J,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,4CAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC9D3C1U,EAHJ,CAAAC,cAAA,aAAyC,aAEX,YACA;QAAAD,EAAA,CAAAU,MAAA,0BAAmB;QAAAV,EAAA,CAAAW,YAAA,EAAK;QAEhDX,EADF,CAAAC,cAAA,aAA8B,aACF;QACxBD,EAAA,CAAAe,SAAA,aAAoF;QACtFf,EAAA,CAAAW,YAAA,EAAM;QAEJX,EADF,CAAAC,cAAA,aAAoB,cACU;QAAAD,EAAA,CAAAU,MAAA,GAA0C;QAAAV,EAAA,CAAAW,YAAA,EAAO;QAC7EX,EAAA,CAAAmB,UAAA,KAAAyT,qDAAA,oBAC2B;QAKjC5U,EAFI,CAAAW,YAAA,EAAM,EACF,EACF;QAGNX,EAAA,CAAAC,cAAA,eAA0D;QAyexDD,EAveA,CAAAmB,UAAA,KAAA0T,kDAAA,oBAAoD,KAAAC,kDAAA,mBA2CA,KAAAC,kDAAA,mBA6CA,KAAAC,kDAAA,mBAgDA,KAAAC,kDAAA,mBAgJa,KAAAC,kDAAA,mBAwDA,KAAAC,kDAAA,oBAsDb,KAAAC,kDAAA,mBAiGa;QA8BrEpV,EADE,CAAAW,YAAA,EAAO,EACH;;;QAphB6BX,EAAA,CAAAY,SAAA,GAAkD;QAAlDZ,EAAA,CAAAqV,WAAA,UAAAV,GAAA,CAAA/J,WAAA,GAAA+J,GAAA,CAAA9J,UAAA,YAAkD;QAGjD7K,EAAA,CAAAY,SAAA,GAA0C;QAA1CZ,EAAA,CAAAsV,kBAAA,UAAAX,GAAA,CAAA/J,WAAA,UAAA+J,GAAA,CAAA9J,UAAA,KAA0C;QAC7D7K,EAAA,CAAAY,SAAA,EAAwC;QAAxCZ,EAAA,CAAA4B,UAAA,SAAA+S,GAAA,CAAA/J,WAAA,QAAA+J,GAAA,CAAA/J,WAAA,KAAwC;QASjD5K,EAAA,CAAAY,SAAA,EAA8B;QAA9BZ,EAAA,CAAA4B,UAAA,cAAA+S,GAAA,CAAAhK,gBAAA,CAA8B;QAE5B3K,EAAA,CAAAY,SAAA,EAAuB;QAAvBZ,EAAA,CAAA4B,UAAA,SAAA+S,GAAA,CAAA/J,WAAA,OAAuB;QA2CvB5K,EAAA,CAAAY,SAAA,EAAuB;QAAvBZ,EAAA,CAAA4B,UAAA,SAAA+S,GAAA,CAAA/J,WAAA,OAAuB;QA6CvB5K,EAAA,CAAAY,SAAA,EAAuB;QAAvBZ,EAAA,CAAA4B,UAAA,SAAA+S,GAAA,CAAA/J,WAAA,OAAuB;QAgDvB5K,EAAA,CAAAY,SAAA,EAAuB;QAAvBZ,EAAA,CAAA4B,UAAA,SAAA+S,GAAA,CAAA/J,WAAA,OAAuB;QAgJvB5K,EAAA,CAAAY,SAAA,EAAuB;QAAvBZ,EAAA,CAAA4B,UAAA,SAAA+S,GAAA,CAAA/J,WAAA,OAAuB;QAwDvB5K,EAAA,CAAAY,SAAA,EAAuB;QAAvBZ,EAAA,CAAA4B,UAAA,SAAA+S,GAAA,CAAA/J,WAAA,OAAuB;QAsDvB5K,EAAA,CAAAY,SAAA,EAAuB;QAAvBZ,EAAA,CAAA4B,UAAA,SAAA+S,GAAA,CAAA/J,WAAA,OAAuB;QAiGvB5K,EAAA,CAAAY,SAAA,EAAuB;QAAvBZ,EAAA,CAAA4B,UAAA,SAAA+S,GAAA,CAAA/J,WAAA,OAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}